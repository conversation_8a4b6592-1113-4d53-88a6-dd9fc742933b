import { JSX, splitProps, createSignal, createEffect } from 'solid-js';
import './Input.css';

export interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: JSX.Element;
  rightIcon?: JSX.Element;
  variant?: 'default' | 'filled';
  size?: 'medium' | 'large';
}

export function Input(props: InputProps) {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'leftIcon',
    'rightIcon',
    'variant',
    'size',
    'class',
    'value',
    'onInput'
  ]);

  const [isFocused, setIsFocused] = createSignal(false);
  const [hasValue, setHasValue] = createSignal(false);

  createEffect(() => {
    setHasValue(!!local.value);
  });

  const containerClasses = () => {
    const base = 'pulse-input-container';
    const variant = `pulse-input-container--${local.variant || 'default'}`;
    const size = `pulse-input-container--${local.size || 'medium'}`;
    const focused = isFocused() ? 'pulse-input-container--focused' : '';
    const hasError = local.error ? 'pulse-input-container--error' : '';
    const hasLeftIcon = local.leftIcon ? 'pulse-input-container--has-left-icon' : '';
    const hasRightIcon = local.rightIcon ? 'pulse-input-container--has-right-icon' : '';
    const custom = local.class || '';
    
    return [base, variant, size, focused, hasError, hasLeftIcon, hasRightIcon, custom]
      .filter(Boolean)
      .join(' ');
  };

  const labelClasses = () => {
    const base = 'pulse-input-label';
    const floating = (isFocused() || hasValue()) ? 'pulse-input-label--floating' : '';
    
    return [base, floating].filter(Boolean).join(' ');
  };

  return (
    <div class="pulse-input-wrapper">
      <div class={containerClasses()}>
        {local.leftIcon && (
          <div class="pulse-input-icon pulse-input-icon--left">
            {local.leftIcon}
          </div>
        )}
        
        <div class="pulse-input-field">
          <input
            class="pulse-input"
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onInput={(e) => {
              setHasValue(!!e.currentTarget.value);
              local.onInput?.(e);
            }}
            value={local.value}
            {...others}
          />
          
          {local.label && (
            <label class={labelClasses()}>
              {local.label}
            </label>
          )}
        </div>
        
        {local.rightIcon && (
          <div class="pulse-input-icon pulse-input-icon--right">
            {local.rightIcon}
          </div>
        )}
      </div>
      
      {(local.error || local.helperText) && (
        <div class="pulse-input-helper">
          {local.error ? (
            <span class="pulse-input-error">{local.error}</span>
          ) : (
            <span class="pulse-input-helper-text">{local.helperText}</span>
          )}
        </div>
      )}
    </div>
  );
}
