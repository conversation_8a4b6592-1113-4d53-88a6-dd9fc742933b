.pulse-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--pulse-radius-medium);
  font-family: var(--pulse-font-family);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  overflow: hidden;
}

.pulse-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pulse-button:focus-visible {
  outline: 2px solid var(--pulse-link);
  outline-offset: 2px;
}

/* Variants */
.pulse-button--primary {
  background-color: var(--pulse-primary);
  color: white;
}

.pulse-button--primary:hover:not(:disabled) {
  background-color: var(--pulse-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--pulse-shadow-medium);
}

.pulse-button--primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--pulse-shadow-small);
}

.pulse-button--secondary {
  background-color: var(--pulse-background-secondary);
  color: var(--pulse-text-primary);
  border: 1px solid var(--pulse-separator-opaque);
}

.pulse-button--secondary:hover:not(:disabled) {
  background-color: var(--pulse-background-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--pulse-shadow-small);
}

.pulse-button--tertiary {
  background-color: transparent;
  color: var(--pulse-link);
}

.pulse-button--tertiary:hover:not(:disabled) {
  background-color: var(--pulse-background-tertiary);
}

.pulse-button--destructive {
  background-color: var(--pulse-error);
  color: white;
}

.pulse-button--destructive:hover:not(:disabled) {
  background-color: #E5342A;
  transform: translateY(-1px);
  box-shadow: var(--pulse-shadow-medium);
}

/* Sizes */
.pulse-button--small {
  padding: var(--pulse-space-xs) var(--pulse-space-md);
  font-size: var(--pulse-text-sm);
  min-height: 32px;
}

.pulse-button--medium {
  padding: var(--pulse-space-sm) var(--pulse-space-lg);
  font-size: var(--pulse-text-base);
  min-height: 44px;
}

.pulse-button--large {
  padding: var(--pulse-space-md) var(--pulse-space-xl);
  font-size: var(--pulse-text-lg);
  min-height: 52px;
}

/* Full width */
.pulse-button--full-width {
  width: 100%;
}

/* Loading state */
.pulse-button--loading .pulse-button__content {
  opacity: 0;
}

.pulse-button__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.pulse-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: pulse-spin 1s linear infinite;
}

@keyframes pulse-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pulse-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pulse-space-xs);
  transition: opacity var(--pulse-transition-fast);
}
