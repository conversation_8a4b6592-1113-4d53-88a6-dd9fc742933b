import { Component, Show, createSignal } from 'solid-js';
import { RSSItem, RSSFeed } from '../types';
import { appStore } from '../stores/app-store';
import { Card } from './Card';
import { AudioPlayer } from './AudioPlayer';
import { VideoPlayer } from './VideoPlayer';
import './ItemCard.css';

interface ItemCardProps {
  item: RSSItem;
  feed?: RSSFeed;
}

export const ItemCard: Component<ItemCardProps> = (props) => {
  const [isExpanded, setIsExpanded] = createSignal(false);
  const [showAudioPlayer, setShowAudioPlayer] = createSignal(false);
  const [showVideoPlayer, setShowVideoPlayer] = createSignal(false);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getContentTypeIcon = () => {
    if (props.item.enclosure) {
      if (props.item.enclosure.type.startsWith('audio/')) {
        return '🎧';
      } else if (props.item.enclosure.type.startsWith('video/')) {
        return '📹';
      }
    }
    return '📄';
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const handleToggleRead = (e: Event) => {
    e.stopPropagation();
    if (props.item.isRead) {
      appStore.markAsUnread(props.item.id);
    } else {
      appStore.markAsRead(props.item.id);
    }
  };

  const handleCardClick = () => {
    if (!props.item.isRead) {
      appStore.markAsRead(props.item.id);
    }
    setIsExpanded(!isExpanded());
    appStore.selectItem(props.item.id);
  };

  const stripHtml = (html: string) => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };

  return (
    <Card
      variant="elevated"
      interactive
      class={`pulse-item-card ${props.item.isRead ? 'pulse-item-card--read' : 'pulse-item-card--unread'}`}
      onClick={handleCardClick}
    >
      <div class="pulse-item-card__content">
        <div class="pulse-item-card__header">
          <div class="pulse-item-card__meta">
            <span class="pulse-item-card__type">
              {getContentTypeIcon()}
            </span>
            <Show when={props.feed && !appStore.state.selectedFeed}>
              <span class="pulse-item-card__feed">
                {props.feed!.title}
              </span>
            </Show>
            <span class="pulse-item-card__date">
              {formatDate(props.item.pubDate)}
            </span>
          </div>
          
          <button
            class={`pulse-item-card__read-toggle ${props.item.isRead ? 'pulse-item-card__read-toggle--read' : ''}`}
            onClick={handleToggleRead}
            title={props.item.isRead ? 'Mark as unread' : 'Mark as read'}
          >
            {props.item.isRead ? '●' : '○'}
          </button>
        </div>

        <h3 class="pulse-item-card__title">
          {props.item.title}
        </h3>

        <p class="pulse-item-card__description">
          {stripHtml(props.item.description)}
        </p>

        <Show when={props.item.enclosure && props.item.duration}>
          <div class="pulse-item-card__media-info">
            <span class="pulse-item-card__duration">
              ⏱️ {formatDuration(props.item.duration!)}
            </span>
            <Show when={props.item.enclosure!.length}>
              <span class="pulse-item-card__size">
                📦 {Math.round(props.item.enclosure!.length! / 1024 / 1024)}MB
              </span>
            </Show>
          </div>
        </Show>

        <Show when={props.item.categories.length > 0}>
          <div class="pulse-item-card__categories">
            {props.item.categories.slice(0, 3).map(category => (
              <span class="pulse-item-card__category">
                {category}
              </span>
            ))}
          </div>
        </Show>

        <Show when={isExpanded()}>
          <div class="pulse-item-card__expanded">
            <Show when={props.item.content && props.item.content !== props.item.description}>
              <div class="pulse-item-card__full-content">
                <div innerHTML={props.item.content} />
              </div>
            </Show>
            
            <div class="pulse-item-card__actions">
              <Show when={props.item.link}>
                <a
                  href={props.item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="pulse-item-card__link"
                  onClick={(e) => e.stopPropagation()}
                >
                  🔗 Open Original
                </a>
              </Show>
              
              <Show when={props.item.enclosure}>
                <button
                  class="pulse-item-card__play"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (props.item.enclosure?.type.startsWith('audio/')) {
                      setShowAudioPlayer(true);
                    } else if (props.item.enclosure?.type.startsWith('video/') || props.item.enclosure?.type === 'video/youtube') {
                      setShowVideoPlayer(true);
                    }
                  }}
                >
                  ▶️ Play
                </button>
              </Show>
            </div>
          </div>
        </Show>
      </div>

      <Show when={showAudioPlayer()}>
        <AudioPlayer
          item={props.item}
          onClose={() => setShowAudioPlayer(false)}
        />
      </Show>

      <Show when={showVideoPlayer()}>
        <VideoPlayer
          item={props.item}
          onClose={() => setShowVideoPlayer(false)}
        />
      </Show>
    </Card>
  );
};
