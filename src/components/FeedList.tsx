import { Component, For, Show, createSignal, createEffect } from 'solid-js';
import { appStore } from '../stores/app-store';
import { Button } from './Button';
import './FeedList.css';

export const FeedList: Component = () => {
  const [showSyncButton, setShowSyncButton] = createSignal(true);
  const [isPulling, setIsPulling] = createSignal(false);
  const [pullDistance, setPullDistance] = createSignal(0);

  let containerRef: HTMLDivElement | undefined;
  let startY = 0;
  let isAtTop = true;

  const getFeedTypeIcon = (type: string) => {
    switch (type) {
      case 'audio': return '🎧';
      case 'video': return '📹';
      case 'text': return '📄';
      default: return '📡';
    }
  };

  const getUnreadCount = (feedId: string) => {
    return appStore.state.items.filter(item => 
      item.feedId === feedId && !item.isRead
    ).length;
  };

  const handleFeedSelect = (feedId: string) => {
    appStore.selectFeed(
      appStore.state.selectedFeed === feedId ? undefined : feedId
    );
  };

  const handleUnsubscribe = async (feedId: string, feedTitle: string) => {
    if (confirm(`Are you sure you want to unsubscribe from "${feedTitle}"? This will remove all downloaded content.`)) {
      try {
        await appStore.unsubscribeFrom(feedId);
      } catch (error) {
        console.error('Failed to unsubscribe:', error);
        alert('Failed to unsubscribe from feed. Please try again.');
      }
    }
  };

  const handleSyncFeeds = async () => {
    await appStore.syncFeeds();
  };

  // Pull-to-refresh functionality
  const handleTouchStart = (e: TouchEvent) => {
    if (!containerRef) return;
    startY = e.touches[0].clientY;
    isAtTop = containerRef.scrollTop === 0;
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!containerRef || !isAtTop) return;

    const currentY = e.touches[0].clientY;
    const diff = currentY - startY;

    if (diff > 0) {
      e.preventDefault();
      const distance = Math.min(diff * 0.5, 100); // Damping effect
      setPullDistance(distance);
      setIsPulling(distance > 50);
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance() > 50) {
      await handleSyncFeeds();
    }
    setPullDistance(0);
    setIsPulling(false);
  };

  createEffect(() => {
    if (containerRef) {
      containerRef.addEventListener('touchstart', handleTouchStart, { passive: false });
      containerRef.addEventListener('touchmove', handleTouchMove, { passive: false });
      containerRef.addEventListener('touchend', handleTouchEnd);

      return () => {
        containerRef?.removeEventListener('touchstart', handleTouchStart);
        containerRef?.removeEventListener('touchmove', handleTouchMove);
        containerRef?.removeEventListener('touchend', handleTouchEnd);
      };
    }
  });

  const formatLastUpdated = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div class="pulse-feed-list">
      <Show when={pullDistance() > 0}>
        <div
          class="pulse-feed-list__pull-indicator"
          style={{ transform: `translateY(${pullDistance()}px)` }}
        >
          <div class={`pulse-feed-list__pull-icon ${isPulling() ? 'active' : ''}`}>
            {isPulling() ? '🔄' : '⬇️'}
          </div>
          <span>{isPulling() ? 'Release to refresh' : 'Pull to refresh'}</span>
        </div>
      </Show>
      <div class="pulse-feed-list__header">
        <h2 class="pulse-feed-list__title">Feeds</h2>
        <div class="pulse-feed-list__actions">
          <Show when={showSyncButton()}>
            <Button
              variant="tertiary"
              size="small"
              onClick={handleSyncFeeds}
              loading={appStore.isSyncing}
              disabled={appStore.isSyncing}
              title="Sync all feeds"
            >
              🔄
            </Button>
          </Show>
        </div>
      </div>

      <div
        class="pulse-feed-list__content"
        ref={containerRef}
        style={{ transform: `translateY(${pullDistance()}px)` }}
      >
        <Show when={appStore.state.feeds.length === 0} fallback={
          <div class="pulse-feed-list__feeds">
            <For each={appStore.state.feeds}>
              {(feed) => {
                const unreadCount = () => getUnreadCount(feed.id);
                const isSelected = () => appStore.state.selectedFeed === feed.id;
                
                return (
                  <div
                    class={`pulse-feed-item ${isSelected() ? 'pulse-feed-item--selected' : ''}`}
                  >
                    <div
                      class="pulse-feed-item__main"
                      onClick={() => handleFeedSelect(feed.id)}
                    >
                      <div class="pulse-feed-item__icon">
                        {getFeedTypeIcon(feed.type)}
                      </div>

                      <div class="pulse-feed-item__content">
                        <div class="pulse-feed-item__header">
                          <h3 class="pulse-feed-item__title">
                            {feed.title}
                          </h3>
                          <Show when={unreadCount() > 0}>
                            <span class="pulse-feed-item__badge">
                              {unreadCount()}
                            </span>
                          </Show>
                        </div>

                        <p class="pulse-feed-item__description">
                          {feed.description}
                        </p>

                        <div class="pulse-feed-item__meta">
                          <span class="pulse-feed-item__updated">
                            {formatLastUpdated(feed.lastUpdated)}
                          </span>
                          <Show when={!feed.isActive}>
                            <span class="pulse-feed-item__status">
                              Paused
                            </span>
                          </Show>
                        </div>
                      </div>
                    </div>

                    <div class="pulse-feed-item__actions">
                      <button
                        class="pulse-feed-item__unsubscribe"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUnsubscribe(feed.id, feed.title);
                        }}
                        title="Unsubscribe from this feed"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                );
              }}
            </For>
          </div>
        }>
          <div class="pulse-feed-list__empty">
            <p>No feeds yet</p>
          </div>
        </Show>
      </div>

      <Show when={appStore.state.lastSync}>
        <div class="pulse-feed-list__footer">
          <span class="pulse-feed-list__sync-info">
            Last sync: {formatLastUpdated(appStore.state.lastSync)}
          </span>
        </div>
      </Show>
    </div>
  );
};
