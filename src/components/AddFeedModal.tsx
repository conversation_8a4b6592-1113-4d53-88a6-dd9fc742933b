import { Component, createSignal, Show } from 'solid-js';
import { appStore } from '../stores/app-store';
import { Button } from './Button';
import { Input } from './Input';
import { Card } from './Card';
import './AddFeedModal.css';

interface AddFeedModalProps {
  onClose: () => void;
}

export const AddFeedModal: Component<AddFeedModalProps> = (props) => {
  const [url, setUrl] = createSignal('');
  const [error, setError] = createSignal('');
  const [isValidating, setIsValidating] = createSignal(false);
  const [validationResult, setValidationResult] = createSignal<any>(null);

  const handleValidate = async () => {
    if (!url().trim()) {
      setError('Please enter a feed URL');
      return;
    }

    setError('');
    setIsValidating(true);

    try {
      console.log('Validating feed URL:', url().trim());
      const result = await appStore.validateFeedUrl(url().trim());
      console.log('Validation result:', result);

      if (result.isValid) {
        setValidationResult(result);
        setError('');
      } else {
        let errorMessage = result.error || 'Invalid feed URL';

        // Provide helpful suggestions for common issues
        if (errorMessage.includes('CORS') || errorMessage.includes('proxies failed')) {
          errorMessage += '\n\nTip: Some feeds (like YouTube) may take a moment to load due to CORS restrictions. Please try again.';
        }

        setError(errorMessage);
        setValidationResult(null);
      }
    } catch (err) {
      console.error('Validation error:', err);
      setError('Failed to validate feed. Please check the URL and try again.');
      setValidationResult(null);
    } finally {
      setIsValidating(false);
    }
  };

  const handleSubscribe = async () => {
    if (!validationResult()) return;
    
    try {
      await appStore.subscribeTo(url().trim());
      props.onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to feed');
    }
  };

  const handleUrlChange = (value: string) => {
    setUrl(value);
    setError('');
    setValidationResult(null);
  };

  const getFeedTypeIcon = (type: string) => {
    switch (type) {
      case 'audio': return '🎧';
      case 'video': return '📹';
      case 'text': return '📄';
      default: return '📡';
    }
  };

  const getFeedTypeLabel = (type: string) => {
    switch (type) {
      case 'audio': return 'Podcast';
      case 'video': return 'Video Feed';
      case 'text': return 'Blog/News';
      default: return 'RSS Feed';
    }
  };

  return (
    <div class="pulse-modal-overlay" onClick={props.onClose}>
      <div class="pulse-modal" onClick={(e) => e.stopPropagation()}>
        <div class="pulse-modal__header">
          <h2>Add RSS Feed</h2>
          <button class="pulse-modal__close" onClick={props.onClose}>
            ✕
          </button>
        </div>
        
        <div class="pulse-modal__content">
          <div class="pulse-add-feed__form">
            <Input
              label="Feed URL"
              placeholder="https://example.com/feed.xml"
              value={url()}
              onInput={(e) => handleUrlChange(e.currentTarget.value)}
              error={error()}
              helperText="Enter the URL of an RSS, Atom, or podcast feed"
            />
            
            <div class="pulse-add-feed__actions">
              <Button
                variant="primary"
                onClick={handleValidate}
                loading={isValidating()}
                disabled={!url().trim() || isValidating()}
                fullWidth
              >
                {validationResult() ? 'Validated ✓' : 'Validate Feed'}
              </Button>
            </div>
          </div>

          <Show when={validationResult()}>
            <Card variant="elevated" class="pulse-add-feed__preview">
              <div class="pulse-feed-preview">
                <div class="pulse-feed-preview__header">
                  <div class="pulse-feed-preview__icon">
                    {getFeedTypeIcon(validationResult().feedType)}
                  </div>
                  <div class="pulse-feed-preview__info">
                    <h3 class="pulse-feed-preview__title">
                      {validationResult().title}
                    </h3>
                    <p class="pulse-feed-preview__type">
                      {getFeedTypeLabel(validationResult().feedType)}
                    </p>
                  </div>
                </div>
                
                <Show when={validationResult().description}>
                  <p class="pulse-feed-preview__description">
                    {validationResult().description}
                  </p>
                </Show>
                
                <div class="pulse-feed-preview__stats">
                  <span class="pulse-feed-preview__stat">
                    📊 {validationResult().itemCount || 0} items
                  </span>
                </div>
              </div>
            </Card>
          </Show>
        </div>
        
        <div class="pulse-modal__footer">
          <Button
            variant="secondary"
            onClick={props.onClose}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSubscribe}
            disabled={!validationResult() || appStore.state.isLoading}
            loading={appStore.state.isLoading}
          >
            Subscribe
          </Button>
        </div>
      </div>
    </div>
  );
};
