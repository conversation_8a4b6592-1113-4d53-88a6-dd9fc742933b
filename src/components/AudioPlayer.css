.pulse-audio-player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--pulse-background-secondary);
  border-top: 1px solid var(--pulse-separator);
  box-shadow: var(--pulse-shadow-large);
  z-index: 1000;
  backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.95);
}

@media (prefers-color-scheme: dark) {
  .pulse-audio-player {
    background-color: rgba(28, 28, 30, 0.95);
  }
}

.pulse-audio-player__header {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-md);
  padding: var(--pulse-space-md) var(--pulse-space-lg);
  border-bottom: 1px solid var(--pulse-separator);
}

.pulse-audio-player__close {
  background: none;
  border: none;
  font-size: var(--pulse-text-lg);
  color: var(--pulse-text-tertiary);
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: 50%;
  transition: all var(--pulse-transition-fast);
  flex-shrink: 0;
}

.pulse-audio-player__close:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
}

.pulse-audio-player__title {
  margin: 0;
  font-size: var(--pulse-text-base);
  font-weight: 600;
  color: var(--pulse-text-primary);
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pulse-audio-player__content {
  padding: var(--pulse-space-lg);
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-lg);
}

.pulse-audio-player__progress {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-md);
}

.pulse-audio-player__time {
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
  font-variant-numeric: tabular-nums;
  min-width: 50px;
  text-align: center;
}

.pulse-audio-player__progress-container {
  flex: 1;
  position: relative;
}

.pulse-audio-player__progress-bar {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--pulse-separator);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.pulse-audio-player__progress-bar::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--pulse-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--pulse-shadow-small);
}

.pulse-audio-player__progress-bar::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--pulse-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--pulse-shadow-small);
}

.pulse-audio-player__controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--pulse-space-xl);
}

.pulse-audio-player__play {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--pulse-primary);
  color: white;
  border: none;
  font-size: var(--pulse-text-2xl);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--pulse-shadow-medium);
}

.pulse-audio-player__play:hover:not(:disabled) {
  background-color: var(--pulse-primary-dark);
  transform: scale(1.05);
}

.pulse-audio-player__play:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pulse-audio-player__skip {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
  border: none;
  font-size: var(--pulse-text-lg);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-audio-player__skip:hover:not(:disabled) {
  background-color: var(--pulse-primary);
  color: white;
  transform: scale(1.05);
}

.pulse-audio-player__skip:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pulse-audio-player__secondary-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--pulse-space-lg);
  flex-wrap: wrap;
}

.pulse-audio-player__volume {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  min-width: 120px;
}

.pulse-audio-player__volume-slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: var(--pulse-separator);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.pulse-audio-player__volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--pulse-primary);
  cursor: pointer;
}

.pulse-audio-player__speed {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
}

.pulse-audio-player__speed-buttons {
  display: flex;
  gap: var(--pulse-space-xs);
}

.pulse-audio-player__speed-btn {
  background: none;
  border: 1px solid var(--pulse-separator-opaque);
  border-radius: var(--pulse-radius-small);
  padding: 2px 6px;
  font-size: var(--pulse-text-xs);
  color: var(--pulse-text-secondary);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
}

.pulse-audio-player__speed-btn:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
}

.pulse-audio-player__speed-btn.active {
  background-color: var(--pulse-primary);
  color: white;
  border-color: var(--pulse-primary);
}

.pulse-audio-player__error {
  padding: var(--pulse-space-lg);
  text-align: center;
  color: var(--pulse-error);
}

.pulse-audio-player__error-actions {
  display: flex;
  gap: var(--pulse-space-md);
  justify-content: center;
  margin-top: var(--pulse-space-md);
  flex-wrap: wrap;
}

.pulse-audio-player__error button {
  background-color: var(--pulse-primary);
  color: white;
  border: none;
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-sm) var(--pulse-space-lg);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  font-size: var(--pulse-text-sm);
}

.pulse-audio-player__error button:hover {
  background-color: var(--pulse-primary-dark);
}

.pulse-audio-player__external-link {
  background-color: var(--pulse-link) !important;
}

.pulse-audio-player__external-link:hover {
  background-color: #0056CC !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pulse-audio-player__header,
  .pulse-audio-player__content {
    padding: var(--pulse-space-md);
  }
  
  .pulse-audio-player__controls {
    gap: var(--pulse-space-lg);
  }
  
  .pulse-audio-player__play {
    width: 56px;
    height: 56px;
    font-size: var(--pulse-text-xl);
  }
  
  .pulse-audio-player__skip {
    width: 40px;
    height: 40px;
    font-size: var(--pulse-text-base);
  }
  
  .pulse-audio-player__secondary-controls {
    flex-direction: column;
    gap: var(--pulse-space-md);
    align-items: stretch;
  }
  
  .pulse-audio-player__volume {
    min-width: auto;
  }
  
  .pulse-audio-player__speed {
    justify-content: center;
  }
}
