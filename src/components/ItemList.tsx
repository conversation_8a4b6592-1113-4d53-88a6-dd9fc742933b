import { Component, For, Show, createMemo } from 'solid-js';
import { appStore } from '../stores/app-store';
import { ItemCard } from './ItemCard';
import './ItemList.css';

export const ItemList: Component = () => {
  const filteredItems = createMemo(() => {
    const selectedFeed = appStore.state.selectedFeed;
    if (!selectedFeed) {
      // Show all items sorted by date
      return appStore.state.items
        .slice()
        .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());
    }
    
    // Show items for selected feed
    return appStore.state.items
      .filter(item => item.feedId === selectedFeed)
      .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());
  });

  const selectedFeed = createMemo(() => {
    const feedId = appStore.state.selectedFeed;
    return feedId ? appStore.state.feeds.find(f => f.id === feedId) : null;
  });

  const getHeaderTitle = () => {
    const feed = selectedFeed();
    if (feed) {
      return feed.title;
    }
    return 'Latest Items';
  };

  const getHeaderSubtitle = () => {
    const feed = selectedFeed();
    const itemCount = filteredItems().length;
    
    if (feed) {
      const unreadCount = filteredItems().filter(item => !item.isRead).length;
      return `${itemCount} items • ${unreadCount} unread`;
    }
    
    return `${itemCount} items from all feeds`;
  };

  return (
    <div class="pulse-item-list">
      <div class="pulse-item-list__header">
        <div class="pulse-item-list__header-content">
          <h2 class="pulse-item-list__title">
            {getHeaderTitle()}
          </h2>
          <p class="pulse-item-list__subtitle">
            {getHeaderSubtitle()}
          </p>
        </div>
        
        <Show when={selectedFeed()}>
          <div class="pulse-item-list__actions">
            <button
              class="pulse-item-list__back"
              onClick={() => appStore.selectFeed(undefined)}
              title="Back to all items"
            >
              ← All Items
            </button>
          </div>
        </Show>
      </div>

      <div class="pulse-item-list__content">
        <Show when={filteredItems().length > 0} fallback={
          <div class="pulse-item-list__empty">
            <div class="pulse-item-list__empty-icon">
              {selectedFeed() ? '📭' : '📪'}
            </div>
            <h3>No items yet</h3>
            <p>
              {selectedFeed() 
                ? 'This feed doesn\'t have any items yet. Try refreshing or check back later.'
                : 'Add some feeds to start seeing content here.'
              }
            </p>
          </div>
        }>
          <div class="pulse-item-list__items">
            <For each={filteredItems()}>
              {(item) => (
                <ItemCard 
                  item={item} 
                  feed={appStore.state.feeds.find(f => f.id === item.feedId)}
                />
              )}
            </For>
          </div>
        </Show>
      </div>
    </div>
  );
};
