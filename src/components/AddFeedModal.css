.pulse-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--pulse-space-lg);
  backdrop-filter: blur(8px);
}

.pulse-modal {
  background-color: var(--pulse-background-secondary);
  border-radius: var(--pulse-radius-large);
  box-shadow: var(--pulse-shadow-large);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pulse-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--pulse-space-lg);
  border-bottom: 1px solid var(--pulse-separator);
}

.pulse-modal__header h2 {
  margin: 0;
  color: var(--pulse-text-primary);
  font-size: var(--pulse-text-xl);
}

.pulse-modal__close {
  background: none;
  border: none;
  font-size: var(--pulse-text-lg);
  color: var(--pulse-text-tertiary);
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: var(--pulse-radius-small);
  transition: all var(--pulse-transition-fast);
}

.pulse-modal__close:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
}

.pulse-modal__content {
  flex: 1;
  padding: var(--pulse-space-lg);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-lg);
}

.pulse-modal__footer {
  display: flex;
  gap: var(--pulse-space-md);
  padding: var(--pulse-space-lg);
  border-top: 1px solid var(--pulse-separator);
  justify-content: flex-end;
}

.pulse-add-feed__form {
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-lg);
}

.pulse-add-feed__actions {
  display: flex;
  gap: var(--pulse-space-md);
}

.pulse-add-feed__preview {
  margin-top: var(--pulse-space-md);
}

.pulse-feed-preview {
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-md);
}

.pulse-feed-preview__header {
  display: flex;
  align-items: flex-start;
  gap: var(--pulse-space-md);
}

.pulse-feed-preview__icon {
  font-size: var(--pulse-text-3xl);
  flex-shrink: 0;
}

.pulse-feed-preview__info {
  flex: 1;
  min-width: 0;
}

.pulse-feed-preview__title {
  margin: 0;
  font-size: var(--pulse-text-lg);
  font-weight: 600;
  color: var(--pulse-text-primary);
  line-height: var(--pulse-leading-tight);
}

.pulse-feed-preview__type {
  margin: var(--pulse-space-xs) 0 0 0;
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-tertiary);
  font-weight: 500;
}

.pulse-feed-preview__description {
  color: var(--pulse-text-secondary);
  font-size: var(--pulse-text-sm);
  line-height: var(--pulse-leading-normal);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-feed-preview__stats {
  display: flex;
  gap: var(--pulse-space-md);
  padding-top: var(--pulse-space-sm);
  border-top: 1px solid var(--pulse-separator);
}

.pulse-feed-preview__stat {
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--pulse-space-xs);
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .pulse-modal-overlay {
    padding: var(--pulse-space-md);
  }
  
  .pulse-modal {
    max-height: 95vh;
  }
  
  .pulse-modal__header,
  .pulse-modal__content,
  .pulse-modal__footer {
    padding: var(--pulse-space-md);
  }
  
  .pulse-modal__footer {
    flex-direction: column;
  }
  
  .pulse-feed-preview__header {
    gap: var(--pulse-space-sm);
  }
  
  .pulse-feed-preview__icon {
    font-size: var(--pulse-text-2xl);
  }
}
