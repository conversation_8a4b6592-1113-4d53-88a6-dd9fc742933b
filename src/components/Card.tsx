import { JSX, splitProps } from 'solid-js';
import './Card.css';

export interface CardProps extends JSX.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'small' | 'medium' | 'large';
  interactive?: boolean;
}

export function Card(props: CardProps) {
  const [local, others] = splitProps(props, [
    'variant',
    'padding',
    'interactive',
    'children',
    'class'
  ]);

  const classes = () => {
    const base = 'pulse-card';
    const variant = `pulse-card--${local.variant || 'default'}`;
    const padding = `pulse-card--padding-${local.padding || 'medium'}`;
    const interactive = local.interactive ? 'pulse-card--interactive' : '';
    const custom = local.class || '';
    
    return [base, variant, padding, interactive, custom]
      .filter(Boolean)
      .join(' ');
  };

  return (
    <div class={classes()} {...others}>
      {local.children}
    </div>
  );
}
