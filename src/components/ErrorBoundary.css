.pulse-error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  min-height: 100dvh;
  padding: var(--pulse-space-lg);
  background-color: var(--pulse-background);
}

.pulse-error-boundary__card {
  max-width: 500px;
  width: 100%;
}

.pulse-error-boundary__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--pulse-space-lg);
}

.pulse-error-boundary__icon {
  font-size: 4rem;
  opacity: 0.8;
}

.pulse-error-boundary__title {
  margin: 0;
  color: var(--pulse-text-primary);
  font-size: var(--pulse-text-2xl);
}

.pulse-error-boundary__message {
  margin: 0;
  color: var(--pulse-text-secondary);
  line-height: var(--pulse-leading-relaxed);
}

.pulse-error-boundary__details {
  width: 100%;
  margin-top: var(--pulse-space-md);
}

.pulse-error-boundary__details summary {
  cursor: pointer;
  color: var(--pulse-text-tertiary);
  font-size: var(--pulse-text-sm);
  margin-bottom: var(--pulse-space-sm);
}

.pulse-error-boundary__details summary:hover {
  color: var(--pulse-text-secondary);
}

.pulse-error-boundary__error {
  background-color: var(--pulse-background-tertiary);
  border: 1px solid var(--pulse-separator);
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-md);
  font-family: var(--pulse-font-family-mono);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
  text-align: left;
}

.pulse-error-boundary__actions {
  display: flex;
  gap: var(--pulse-space-md);
  flex-wrap: wrap;
  justify-content: center;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .pulse-error-boundary {
    padding: var(--pulse-space-md);
  }
  
  .pulse-error-boundary__icon {
    font-size: 3rem;
  }
  
  .pulse-error-boundary__title {
    font-size: var(--pulse-text-xl);
  }
  
  .pulse-error-boundary__actions {
    flex-direction: column;
    align-items: stretch;
  }
}
