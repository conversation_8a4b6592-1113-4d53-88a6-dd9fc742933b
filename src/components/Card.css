.pulse-card {
  background-color: var(--pulse-background-secondary);
  border-radius: var(--pulse-radius-medium);
  transition: all var(--pulse-transition-fast);
  overflow: hidden;
}

/* Variants */
.pulse-card--default {
  border: 1px solid var(--pulse-separator);
}

.pulse-card--elevated {
  box-shadow: var(--pulse-shadow-small);
  border: none;
}

.pulse-card--outlined {
  border: 1px solid var(--pulse-separator-opaque);
  box-shadow: none;
}

/* Padding */
.pulse-card--padding-none {
  padding: 0;
}

.pulse-card--padding-small {
  padding: var(--pulse-space-sm);
}

.pulse-card--padding-medium {
  padding: var(--pulse-space-md);
}

.pulse-card--padding-large {
  padding: var(--pulse-space-lg);
}

/* Interactive */
.pulse-card--interactive {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.pulse-card--interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--pulse-shadow-medium);
}

.pulse-card--interactive:active {
  transform: translateY(0);
  box-shadow: var(--pulse-shadow-small);
}
