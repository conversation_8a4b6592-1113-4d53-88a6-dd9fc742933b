.pulse-input-wrapper {
  width: 100%;
}

.pulse-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--pulse-background-secondary);
  border: 1px solid var(--pulse-separator);
  border-radius: var(--pulse-radius-medium);
  transition: all var(--pulse-transition-fast);
  overflow: hidden;
}

.pulse-input-container--focused {
  border-color: var(--pulse-primary);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.pulse-input-container--error {
  border-color: var(--pulse-error);
}

.pulse-input-container--error.pulse-input-container--focused {
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

/* Variants */
.pulse-input-container--filled {
  background-color: var(--pulse-background-tertiary);
  border: none;
}

.pulse-input-container--filled.pulse-input-container--focused {
  background-color: var(--pulse-background-secondary);
  border: 1px solid var(--pulse-primary);
}

/* Sizes */
.pulse-input-container--medium {
  min-height: 44px;
}

.pulse-input-container--large {
  min-height: 52px;
}

/* Icons */
.pulse-input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--pulse-text-tertiary);
  transition: color var(--pulse-transition-fast);
}

.pulse-input-icon--left {
  padding-left: var(--pulse-space-md);
  padding-right: var(--pulse-space-xs);
}

.pulse-input-icon--right {
  padding-left: var(--pulse-space-xs);
  padding-right: var(--pulse-space-md);
}

.pulse-input-container--focused .pulse-input-icon {
  color: var(--pulse-primary);
}

/* Field */
.pulse-input-field {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.pulse-input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-family: var(--pulse-font-family);
  font-size: var(--pulse-text-base);
  color: var(--pulse-text-primary);
  padding: var(--pulse-space-md);
  transition: all var(--pulse-transition-fast);
}

.pulse-input-container--has-left-icon .pulse-input {
  padding-left: 0;
}

.pulse-input-container--has-right-icon .pulse-input {
  padding-right: 0;
}

.pulse-input::placeholder {
  color: var(--pulse-text-quaternary);
}

/* Label */
.pulse-input-label {
  position: absolute;
  left: var(--pulse-space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--pulse-text-tertiary);
  font-size: var(--pulse-text-base);
  pointer-events: none;
  transition: all var(--pulse-transition-fast);
  background-color: var(--pulse-background-secondary);
  padding: 0 var(--pulse-space-xs);
  border-radius: var(--pulse-radius-small);
}

.pulse-input-container--has-left-icon .pulse-input-label {
  left: 40px;
}

.pulse-input-label--floating {
  top: 0;
  transform: translateY(-50%);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-primary);
}

.pulse-input-container--filled .pulse-input-label {
  background-color: var(--pulse-background-tertiary);
}

.pulse-input-container--filled.pulse-input-container--focused .pulse-input-label {
  background-color: var(--pulse-background-secondary);
}

/* Helper text */
.pulse-input-helper {
  margin-top: var(--pulse-space-xs);
  padding: 0 var(--pulse-space-md);
}

.pulse-input-helper-text {
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-tertiary);
}

.pulse-input-error {
  font-size: var(--pulse-text-sm);
  color: var(--pulse-error);
}
