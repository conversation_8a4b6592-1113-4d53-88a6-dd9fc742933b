.pulse-item-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--pulse-background);
}

.pulse-item-list__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--pulse-space-lg);
  background-color: var(--pulse-background);
  border-bottom: 1px solid var(--pulse-separator);
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
  background-color: rgba(242, 242, 247, 0.95);
}

@media (prefers-color-scheme: dark) {
  .pulse-item-list__header {
    background-color: rgba(0, 0, 0, 0.95);
  }
}

.pulse-item-list__header-content {
  flex: 1;
  min-width: 0;
}

.pulse-item-list__title {
  margin: 0;
  font-size: var(--pulse-text-2xl);
  font-weight: 700;
  color: var(--pulse-text-primary);
  line-height: var(--pulse-leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-item-list__subtitle {
  margin: var(--pulse-space-xs) 0 0 0;
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
}

.pulse-item-list__actions {
  display: flex;
  gap: var(--pulse-space-sm);
  margin-left: var(--pulse-space-md);
}

.pulse-item-list__back {
  background: none;
  border: 1px solid var(--pulse-separator-opaque);
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-xs) var(--pulse-space-md);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  font-family: var(--pulse-font-family);
}

.pulse-item-list__back:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
  border-color: var(--pulse-primary);
}

.pulse-item-list__content {
  flex: 1;
  overflow-y: auto;
}

.pulse-item-list__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--pulse-space-2xl);
  min-height: 400px;
  gap: var(--pulse-space-lg);
}

.pulse-item-list__empty-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.pulse-item-list__empty h3 {
  margin: 0;
  color: var(--pulse-text-primary);
  font-size: var(--pulse-text-xl);
}

.pulse-item-list__empty p {
  margin: 0;
  color: var(--pulse-text-secondary);
  max-width: 400px;
  line-height: var(--pulse-leading-relaxed);
}

.pulse-item-list__items {
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-md);
  padding: var(--pulse-space-lg);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pulse-item-list__header {
    padding: var(--pulse-space-md);
    flex-direction: column;
    gap: var(--pulse-space-md);
    align-items: stretch;
  }
  
  .pulse-item-list__actions {
    margin-left: 0;
    justify-content: flex-start;
  }
  
  .pulse-item-list__title {
    font-size: var(--pulse-text-xl);
  }
  
  .pulse-item-list__items {
    padding: var(--pulse-space-md);
    gap: var(--pulse-space-sm);
  }
  
  .pulse-item-list__empty {
    padding: var(--pulse-space-xl);
    min-height: 300px;
  }
  
  .pulse-item-list__empty-icon {
    font-size: 3rem;
  }
}
