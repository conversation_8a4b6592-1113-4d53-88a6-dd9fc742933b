.pulse-video-player {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.95);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.pulse-video-player__header {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-md);
  padding: var(--pulse-space-md) var(--pulse-space-lg);
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.pulse-video-player__close {
  background: none;
  border: none;
  font-size: var(--pulse-text-lg);
  color: white;
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: 50%;
  transition: all var(--pulse-transition-fast);
  flex-shrink: 0;
}

.pulse-video-player__close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.pulse-video-player__title {
  margin: 0;
  font-size: var(--pulse-text-base);
  font-weight: 600;
  color: white;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pulse-video-player__container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: none;
}

.pulse-video-player__video {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  cursor: pointer;
}

.pulse-video-player__controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--pulse-space-xl) var(--pulse-space-lg) var(--pulse-space-lg);
  transition: opacity var(--pulse-transition-normal);
}

.pulse-video-player__progress {
  margin-bottom: var(--pulse-space-md);
}

.pulse-video-player__progress-bar {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.pulse-video-player__progress-bar::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--pulse-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--pulse-shadow-small);
}

.pulse-video-player__progress-bar::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--pulse-primary);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--pulse-shadow-small);
}

.pulse-video-player__control-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--pulse-space-lg);
}

.pulse-video-player__left-controls,
.pulse-video-player__right-controls {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-md);
}

.pulse-video-player__play {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--pulse-primary);
  color: white;
  border: none;
  font-size: var(--pulse-text-xl);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--pulse-shadow-medium);
}

.pulse-video-player__play:hover:not(:disabled) {
  background-color: var(--pulse-primary-dark);
  transform: scale(1.05);
}

.pulse-video-player__play:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pulse-video-player__time {
  color: white;
  font-size: var(--pulse-text-sm);
  font-variant-numeric: tabular-nums;
  white-space: nowrap;
}

.pulse-video-player__volume {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  color: white;
}

.pulse-video-player__volume-slider {
  width: 80px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.pulse-video-player__volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.pulse-video-player__fullscreen {
  background: none;
  border: none;
  color: white;
  font-size: var(--pulse-text-lg);
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: var(--pulse-radius-small);
  transition: all var(--pulse-transition-fast);
}

.pulse-video-player__fullscreen:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.pulse-video-player__youtube-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pulse-video-player__youtube-iframe {
  width: 100%;
  height: 100%;
  min-height: 400px;
  max-width: 1200px;
  max-height: 675px;
  aspect-ratio: 16/9;
}

.pulse-video-player__youtube-controls {
  position: absolute;
  bottom: var(--pulse-space-lg);
  right: var(--pulse-space-lg);
}

.pulse-video-player__youtube-open {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-sm) var(--pulse-space-md);
  font-size: var(--pulse-text-sm);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  backdrop-filter: blur(10px);
}

.pulse-video-player__youtube-open:hover {
  background-color: rgba(0, 0, 0, 0.9);
  border-color: white;
}

.pulse-video-player__youtube {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  gap: var(--pulse-space-lg);
  padding: var(--pulse-space-2xl);
  max-width: 400px;
}

.pulse-video-player__youtube-icon {
  font-size: 4rem;
  opacity: 0.8;
}

.pulse-video-player__youtube h4 {
  margin: 0;
  font-size: var(--pulse-text-xl);
  color: white;
}

.pulse-video-player__youtube p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: var(--pulse-leading-relaxed);
}

.pulse-video-player__youtube-btn {
  background-color: #FF0000;
  color: white;
  border: none;
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-md) var(--pulse-space-xl);
  font-size: var(--pulse-text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
}

.pulse-video-player__youtube-btn:hover {
  background-color: #CC0000;
  transform: translateY(-2px);
  box-shadow: var(--pulse-shadow-medium);
}

.pulse-video-player__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  gap: var(--pulse-space-lg);
  padding: var(--pulse-space-2xl);
}

.pulse-video-player__error button {
  background-color: var(--pulse-primary);
  color: white;
  border: none;
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-sm) var(--pulse-space-lg);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
}

.pulse-video-player__error button:hover {
  background-color: var(--pulse-primary-dark);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pulse-video-player__header {
    padding: var(--pulse-space-sm) var(--pulse-space-md);
  }
  
  .pulse-video-player__controls {
    padding: var(--pulse-space-lg) var(--pulse-space-md) var(--pulse-space-md);
  }
  
  .pulse-video-player__control-bar {
    flex-direction: column;
    gap: var(--pulse-space-md);
    align-items: stretch;
  }
  
  .pulse-video-player__left-controls,
  .pulse-video-player__right-controls {
    justify-content: center;
  }
  
  .pulse-video-player__volume {
    justify-content: center;
  }
  
  .pulse-video-player__volume-slider {
    width: 120px;
  }
  
  .pulse-video-player__youtube {
    padding: var(--pulse-space-xl);
  }
  
  .pulse-video-player__youtube-icon {
    font-size: 3rem;
  }
}
