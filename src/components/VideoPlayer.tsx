import { Component, createSignal, createEffect, onCleanup, Show } from 'solid-js';
import { RSSItem } from '../types';
import { appStore } from '../stores/app-store';
import './VideoPlayer.css';

interface VideoPlayerProps {
  item: RSSItem;
  onClose: () => void;
}

export const VideoPlayer: Component<VideoPlayerProps> = (props) => {
  let videoRef: HTMLVideoElement | undefined;
  
  const [isPlaying, setIsPlaying] = createSignal(false);
  const [currentTime, setCurrentTime] = createSignal(0);
  const [duration, setDuration] = createSignal(0);
  const [volume, setVolume] = createSignal(0.8);
  const [isFullscreen, setIsFullscreen] = createSignal(false);
  const [showControls, setShowControls] = createSignal(true);
  const [isLoading, setIsLoading] = createSignal(true);
  const [error, setError] = createSignal('');

  let controlsTimeout: number | undefined;

  createEffect(() => {
    if (videoRef) {
      videoRef.volume = volume();
    }
  });

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlay = async () => {
    if (!videoRef) return;
    
    try {
      if (isPlaying()) {
        videoRef.pause();
      } else {
        await videoRef.play();
      }
    } catch (err) {
      console.error('Playback error:', err);
      setError('Failed to play video');
    }
  };

  const handleSeek = (e: Event) => {
    if (!videoRef) return;
    
    const target = e.target as HTMLInputElement;
    const seekTime = (parseFloat(target.value) / 100) * duration();
    videoRef.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const newVolume = parseFloat(target.value) / 100;
    setVolume(newVolume);
  };

  const handleFullscreen = async () => {
    if (!videoRef) return;
    
    try {
      if (!isFullscreen()) {
        await videoRef.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (err) {
      console.error('Fullscreen error:', err);
    }
  };

  const handleMouseMove = () => {
    setShowControls(true);
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    controlsTimeout = setTimeout(() => {
      if (isPlaying()) {
        setShowControls(false);
      }
    }, 3000);
  };

  const setupVideoEvents = () => {
    if (!videoRef) return;

    videoRef.addEventListener('loadstart', () => setIsLoading(true));
    videoRef.addEventListener('canplay', () => setIsLoading(false));
    videoRef.addEventListener('play', () => setIsPlaying(true));
    videoRef.addEventListener('pause', () => setIsPlaying(false));
    videoRef.addEventListener('ended', () => {
      setIsPlaying(false);
      setCurrentTime(0);
      setShowControls(true);
      // Mark as read when finished
      if (!props.item.isRead) {
        appStore.markAsRead(props.item.id);
      }
    });
    
    videoRef.addEventListener('timeupdate', () => {
      const current = videoRef!.currentTime;
      setCurrentTime(current);

      // Update progress for read tracking
      if (duration() > 0) {
        appStore.updateProgress(props.item.id, current, duration());
      }
    });
    
    videoRef.addEventListener('durationchange', () => {
      setDuration(videoRef!.duration || 0);
    });
    
    videoRef.addEventListener('fullscreenchange', () => {
      setIsFullscreen(!!document.fullscreenElement);
    });
    
    videoRef.addEventListener('error', (e) => {
      console.error('Video error:', e);
      setError('Failed to load video');
      setIsLoading(false);
    });
  };

  createEffect(async () => {
    if (videoRef && props.item.enclosure?.url) {
      setupVideoEvents();

      // Handle YouTube URLs
      if (props.item.enclosure.type === 'video/youtube') {
        // For YouTube, we'll show a message to open in YouTube
        setError('YouTube videos must be opened in YouTube app or browser');
        return;
      }

      // Get the appropriate content URL (downloaded or streaming)
      const contentUrl = await appStore.getContentUrl(props.item);
      if (contentUrl) {
        videoRef.src = contentUrl;
        videoRef.load();
      } else {
        setError('Unable to load video content');
      }
    }
  });

  onCleanup(() => {
    if (videoRef) {
      videoRef.pause();
      videoRef.src = '';
    }
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
  });

  const progressPercentage = () => {
    return duration() > 0 ? (currentTime() / duration()) * 100 : 0;
  };

  const isYouTubeVideo = () => {
    return props.item.enclosure?.type === 'video/youtube';
  };

  const openInYouTube = () => {
    if (props.item.enclosure?.url) {
      window.open(props.item.enclosure.url, '_blank');
    } else if (props.item.link) {
      window.open(props.item.link, '_blank');
    }
  };

  const getYouTubeVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/v\/([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  const getYouTubeEmbedUrl = (): string | null => {
    const videoUrl = props.item.enclosure?.url || props.item.link;
    if (!videoUrl) return null;

    const videoId = getYouTubeVideoId(videoUrl);
    if (!videoId) return null;

    return `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&modestbranding=1`;
  };

  return (
    <div class="pulse-video-player">
      <div class="pulse-video-player__header">
        <button class="pulse-video-player__close" onClick={props.onClose}>
          ✕
        </button>
        <h3 class="pulse-video-player__title">{props.item.title}</h3>
      </div>

      <div
        class="pulse-video-player__container"
        onMouseMove={handleMouseMove}
        onMouseLeave={() => isPlaying() && setShowControls(false)}
      >
        <Show when={isYouTubeVideo()} fallback={
          <>
            <video
              ref={videoRef}
              class="pulse-video-player__video"
              preload="metadata"
              onClick={handlePlay}
            />

            <Show when={showControls() || !isPlaying()}>
              <div class="pulse-video-player__controls">
                <div class="pulse-video-player__progress">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progressPercentage()}
                    class="pulse-video-player__progress-bar"
                    onInput={handleSeek}
                    disabled={isLoading()}
                  />
                </div>

                <div class="pulse-video-player__control-bar">
                  <div class="pulse-video-player__left-controls">
                    <button
                      class="pulse-video-player__play"
                      onClick={handlePlay}
                      disabled={isLoading()}
                    >
                      {isLoading() ? '⏳' : isPlaying() ? '⏸️' : '▶️'}
                    </button>

                    <div class="pulse-video-player__time">
                      {formatTime(currentTime())} / {formatTime(duration())}
                    </div>
                  </div>

                  <div class="pulse-video-player__right-controls">
                    <div class="pulse-video-player__volume">
                      <span>🔊</span>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={volume() * 100}
                        class="pulse-video-player__volume-slider"
                        onInput={handleVolumeChange}
                      />
                    </div>

                    <button
                      class="pulse-video-player__fullscreen"
                      onClick={handleFullscreen}
                    >
                      {isFullscreen() ? '⛶' : '⛶'}
                    </button>
                  </div>
                </div>
              </div>
            </Show>

            <Show when={error()}>
              <div class="pulse-video-player__error">
                <p>{error()}</p>
                <button onClick={() => setError('')}>Try Again</button>
              </div>
            </Show>
          </>
        }>
          <div class="pulse-video-player__youtube-container">
            <Show when={getYouTubeEmbedUrl()} fallback={
              <div class="pulse-video-player__youtube">
                <div class="pulse-video-player__youtube-icon">📺</div>
                <h4>YouTube Video</h4>
                <p>This video is hosted on YouTube. Click below to open it in YouTube.</p>
                <button
                  class="pulse-video-player__youtube-btn"
                  onClick={openInYouTube}
                >
                  Open in YouTube
                </button>
              </div>
            }>
              <iframe
                class="pulse-video-player__youtube-iframe"
                src={getYouTubeEmbedUrl()!}
                title={props.item.title}
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
                onLoad={() => {
                  setIsLoading(false);
                  // Mark as read when video is loaded
                  if (!props.item.isRead) {
                    appStore.markAsRead(props.item.id);
                  }
                }}
              />
              <div class="pulse-video-player__youtube-controls">
                <button
                  class="pulse-video-player__youtube-open"
                  onClick={openInYouTube}
                  title="Open in YouTube"
                >
                  Open in YouTube App
                </button>
              </div>
            </Show>
          </div>
        </Show>
      </div>
    </div>
  );
};
