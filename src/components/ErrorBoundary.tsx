import { Component, createSignal, JSX, onMount } from 'solid-js';
import { Button } from './Button';
import { Card } from './Card';
import './ErrorBoundary.css';

interface ErrorBoundaryProps {
  children: JSX.Element;
  fallback?: (error: Error, reset: () => void) => JSX.Element;
}

export const ErrorBoundary: Component<ErrorBoundaryProps> = (props) => {
  const [error, setError] = createSignal<Error | null>(null);

  const reset = () => {
    setError(null);
  };

  onMount(() => {
    // Global error handler
    const handleError = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      setError(event.error);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      setError(new Error(event.reason));
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  });

  const currentError = error();
  
  if (currentError) {
    if (props.fallback) {
      return props.fallback(currentError, reset);
    }

    return (
      <div class="pulse-error-boundary">
        <Card variant="elevated" class="pulse-error-boundary__card">
          <div class="pulse-error-boundary__content">
            <div class="pulse-error-boundary__icon">⚠️</div>
            <h2 class="pulse-error-boundary__title">Oops! Something went wrong</h2>
            <p class="pulse-error-boundary__message">
              We encountered an unexpected error. Don't worry, your data is safe!
            </p>
            
            <details class="pulse-error-boundary__details">
              <summary>Technical Details</summary>
              <pre class="pulse-error-boundary__error">
                {currentError.name}: {currentError.message}
                {currentError.stack && `\n\nStack trace:\n${currentError.stack}`}
              </pre>
            </details>

            <div class="pulse-error-boundary__actions">
              <Button variant="primary" onClick={reset}>
                Try Again
              </Button>
              <Button 
                variant="secondary" 
                onClick={() => window.location.reload()}
              >
                Reload App
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return props.children;
};
