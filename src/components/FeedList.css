.pulse-feed-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--pulse-background-secondary);
  position: relative;
  overflow: hidden;
}

.pulse-feed-list__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--pulse-space-lg) var(--pulse-space-md);
  border-bottom: 1px solid var(--pulse-separator);
  background-color: var(--pulse-background-secondary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.pulse-feed-list__title {
  margin: 0;
  font-size: var(--pulse-text-lg);
  font-weight: 600;
  color: var(--pulse-text-primary);
}

.pulse-feed-list__actions {
  display: flex;
  gap: var(--pulse-space-xs);
}

.pulse-feed-list__content {
  flex: 1;
  overflow-y: auto;
}

.pulse-feed-list__feeds {
  display: flex;
  flex-direction: column;
}

.pulse-feed-list__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--pulse-space-2xl);
  color: var(--pulse-text-tertiary);
  font-size: var(--pulse-text-sm);
}

.pulse-feed-item {
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid var(--pulse-separator);
  transition: all var(--pulse-transition-fast);
  background-color: var(--pulse-background-secondary);
}

.pulse-feed-item__main {
  display: flex;
  align-items: flex-start;
  gap: var(--pulse-space-md);
  padding: var(--pulse-space-md);
  flex: 1;
  cursor: pointer;
}

.pulse-feed-item:hover {
  background-color: var(--pulse-background-tertiary);
}

.pulse-feed-item__actions {
  display: flex;
  align-items: center;
  padding: var(--pulse-space-sm);
}

.pulse-feed-item__unsubscribe {
  background: none;
  border: none;
  font-size: var(--pulse-text-base);
  color: var(--pulse-text-quaternary);
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: var(--pulse-radius-small);
  transition: all var(--pulse-transition-fast);
  opacity: 0;
}

.pulse-feed-item:hover .pulse-feed-item__unsubscribe {
  opacity: 1;
}

.pulse-feed-item__unsubscribe:hover {
  background-color: var(--pulse-error);
  color: white;
  transform: scale(1.1);
}

.pulse-feed-item--selected {
  background-color: var(--pulse-primary);
  color: white;
}

.pulse-feed-item--selected .pulse-feed-item__title,
.pulse-feed-item--selected .pulse-feed-item__description,
.pulse-feed-item--selected .pulse-feed-item__meta {
  color: white;
}

.pulse-feed-item--selected .pulse-feed-item__badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.pulse-feed-item__icon {
  font-size: var(--pulse-text-xl);
  flex-shrink: 0;
  margin-top: var(--pulse-space-xs);
}

.pulse-feed-item__content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-xs);
}

.pulse-feed-item__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--pulse-space-sm);
}

.pulse-feed-item__title {
  margin: 0;
  font-size: var(--pulse-text-base);
  font-weight: 600;
  color: var(--pulse-text-primary);
  line-height: var(--pulse-leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-feed-item__badge {
  background-color: var(--pulse-primary);
  color: white;
  font-size: var(--pulse-text-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  flex-shrink: 0;
}

.pulse-feed-item__description {
  margin: 0;
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
  line-height: var(--pulse-leading-normal);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-feed-item__meta {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  font-size: var(--pulse-text-xs);
  color: var(--pulse-text-tertiary);
}

.pulse-feed-item__updated {
  flex: 1;
}

.pulse-feed-item__status {
  background-color: var(--pulse-warning);
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-weight: 500;
}

.pulse-feed-list__footer {
  padding: var(--pulse-space-sm) var(--pulse-space-md);
  border-top: 1px solid var(--pulse-separator);
  background-color: var(--pulse-background-tertiary);
}

.pulse-feed-list__sync-info {
  font-size: var(--pulse-text-xs);
  color: var(--pulse-text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--pulse-space-xs);
}

.pulse-feed-list__pull-indicator {
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--pulse-space-xs);
  background-color: var(--pulse-background-secondary);
  color: var(--pulse-text-secondary);
  font-size: var(--pulse-text-sm);
  z-index: 20;
  transition: transform var(--pulse-transition-fast);
}

.pulse-feed-list__pull-icon {
  font-size: var(--pulse-text-lg);
  transition: transform var(--pulse-transition-fast);
}

.pulse-feed-list__pull-icon.active {
  animation: pulse-spin 1s linear infinite;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pulse-feed-list__header {
    padding: var(--pulse-space-md) var(--pulse-space-sm);
  }
  
  .pulse-feed-item {
    padding: var(--pulse-space-sm);
    gap: var(--pulse-space-sm);
  }
  
  .pulse-feed-item__icon {
    font-size: var(--pulse-text-lg);
  }
  
  .pulse-feed-item__title {
    font-size: var(--pulse-text-sm);
  }
  
  .pulse-feed-item__description {
    font-size: var(--pulse-text-xs);
    -webkit-line-clamp: 1;
  }
}
