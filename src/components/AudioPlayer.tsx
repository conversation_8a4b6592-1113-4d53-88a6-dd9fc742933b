import { Component, createSignal, createEffect, onCleanup, Show } from 'solid-js';
import { RSSItem } from '../types';
import { appStore } from '../stores/app-store';
import './AudioPlayer.css';

interface AudioPlayerProps {
  item: RSSItem;
  onClose: () => void;
}

export const AudioPlayer: Component<AudioPlayerProps> = (props) => {
  let audioRef: HTMLAudioElement | undefined;
  
  const [isPlaying, setIsPlaying] = createSignal(false);
  const [currentTime, setCurrentTime] = createSignal(0);
  const [duration, setDuration] = createSignal(0);
  const [volume, setVolume] = createSignal(0.8);
  const [playbackRate, setPlaybackRate] = createSignal(1.0);
  const [isLoading, setIsLoading] = createSignal(true);
  const [error, setError] = createSignal('');
  const [showExternalLink, setShowExternalLink] = createSignal(false);

  createEffect(() => {
    if (audioRef) {
      audioRef.volume = volume();
      audioRef.playbackRate = playbackRate();
    }
  });

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlay = async () => {
    if (!audioRef) return;

    try {
      if (isPlaying()) {
        audioRef.pause();
      } else {
        console.log('Attempting to play audio...');
        console.log('Audio ready state:', audioRef.readyState);
        console.log('Audio network state:', audioRef.networkState);

        // Check if audio is ready to play
        if (audioRef.readyState < 2) { // HAVE_CURRENT_DATA
          console.log('Audio not ready, waiting for canplay event');
          setError('Loading audio...');
          return;
        }

        await audioRef.play();
        console.log('Audio playback started successfully');
      }
    } catch (err) {
      console.error('Playback error:', err);
      let errorMessage = 'Failed to play audio';

      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          errorMessage = 'Audio playback blocked. Please interact with the page first.';
        } else if (err.name === 'NotSupportedError') {
          errorMessage = 'Audio format not supported by your browser.';
        } else if (err.name === 'AbortError') {
          errorMessage = 'Audio playback was interrupted.';
        }
      }

      setError(errorMessage);
    }
  };

  const handleSeek = (e: Event) => {
    if (!audioRef) return;
    
    const target = e.target as HTMLInputElement;
    const seekTime = (parseFloat(target.value) / 100) * duration();
    audioRef.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const newVolume = parseFloat(target.value) / 100;
    setVolume(newVolume);
  };

  const handlePlaybackRateChange = (rate: number) => {
    setPlaybackRate(rate);
  };

  const handleSkip = (seconds: number) => {
    if (!audioRef) return;
    
    const newTime = Math.max(0, Math.min(duration(), currentTime() + seconds));
    audioRef.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const setupAudioEvents = () => {
    if (!audioRef) return;

    audioRef.addEventListener('loadstart', () => {
      console.log('Audio loading started');
      setIsLoading(true);
      setError('');
    });

    audioRef.addEventListener('canplay', () => {
      console.log('Audio can play');
      setIsLoading(false);
    });

    audioRef.addEventListener('loadedmetadata', () => {
      console.log('Audio metadata loaded');
      setDuration(audioRef!.duration || 0);
    });

    audioRef.addEventListener('play', () => setIsPlaying(true));
    audioRef.addEventListener('pause', () => setIsPlaying(false));
    audioRef.addEventListener('ended', () => {
      setIsPlaying(false);
      setCurrentTime(0);
      // Mark as read when finished
      if (!props.item.isRead) {
        appStore.markAsRead(props.item.id);
      }
    });

    audioRef.addEventListener('timeupdate', () => {
      const current = audioRef!.currentTime;
      setCurrentTime(current);

      // Update progress for read tracking
      if (duration() > 0) {
        appStore.updateProgress(props.item.id, current, duration());
      }
    });

    audioRef.addEventListener('durationchange', () => {
      setDuration(audioRef!.duration || 0);
    });

    audioRef.addEventListener('error', (e) => {
      console.error('Audio error details:', {
        error: e,
        audioError: audioRef?.error,
        src: audioRef?.src,
        networkState: audioRef?.networkState,
        readyState: audioRef?.readyState
      });

      let errorMessage = 'Failed to load audio';
      let showExternalLink = false;

      if (audioRef?.error) {
        switch (audioRef.error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = 'Audio loading was aborted';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = 'Network error while loading audio. This podcast may not allow direct playback.';
            showExternalLink = true;
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = 'Audio format not supported';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = 'Audio source not supported or blocked by CORS policy. Try opening in external player.';
            showExternalLink = true;
            break;
          default:
            errorMessage = 'Unknown audio error. Try opening in external player.';
            showExternalLink = true;
        }
      }

      setError(errorMessage);
      setShowExternalLink(showExternalLink);
      setIsLoading(false);
    });
  };

  createEffect(async () => {
    if (audioRef && props.item.enclosure?.url) {
      console.log('Setting up audio for:', props.item.title);
      console.log('Enclosure URL:', props.item.enclosure.url);

      setupAudioEvents();

      try {
        // Get the appropriate content URL (downloaded or streaming)
        const contentUrl = await appStore.getContentUrl(props.item);
        console.log('Content URL resolved to:', contentUrl);

        if (contentUrl) {
          // Try without CORS first (for same-origin or CORS-enabled content)
          audioRef.crossOrigin = null;
          audioRef.src = contentUrl;
          audioRef.load();

          // If CORS error occurs, we'll handle it in the error event
        } else {
          setError('Unable to load audio content - no valid URL found');
        }
      } catch (error) {
        console.error('Error setting up audio:', error);
        setError('Failed to set up audio player');
      }
    }
  });

  onCleanup(() => {
    if (audioRef) {
      audioRef.pause();
      audioRef.src = '';
    }
  });

  const progressPercentage = () => {
    return duration() > 0 ? (currentTime() / duration()) * 100 : 0;
  };

  return (
    <div class="pulse-audio-player">
      <div class="pulse-audio-player__header">
        <button class="pulse-audio-player__close" onClick={props.onClose}>
          ✕
        </button>
        <h3 class="pulse-audio-player__title">{props.item.title}</h3>
      </div>

      <audio ref={audioRef} preload="metadata" />

      <Show when={error()} fallback={
        <div class="pulse-audio-player__content">
          <div class="pulse-audio-player__progress">
            <span class="pulse-audio-player__time">
              {formatTime(currentTime())}
            </span>
            <div class="pulse-audio-player__progress-container">
              <input
                type="range"
                min="0"
                max="100"
                value={progressPercentage()}
                class="pulse-audio-player__progress-bar"
                onInput={handleSeek}
                disabled={isLoading()}
              />
            </div>
            <span class="pulse-audio-player__time">
              {formatTime(duration())}
            </span>
          </div>

          <div class="pulse-audio-player__controls">
            <button
              class="pulse-audio-player__skip"
              onClick={() => handleSkip(-30)}
              disabled={isLoading()}
              title="Skip back 30s"
            >
              ⏪
            </button>

            <button
              class="pulse-audio-player__play"
              onClick={handlePlay}
              disabled={isLoading()}
            >
              {isLoading() ? '⏳' : isPlaying() ? '⏸️' : '▶️'}
            </button>

            <button
              class="pulse-audio-player__skip"
              onClick={() => handleSkip(30)}
              disabled={isLoading()}
              title="Skip forward 30s"
            >
              ⏩
            </button>
          </div>

          <div class="pulse-audio-player__secondary-controls">
            <div class="pulse-audio-player__volume">
              <span>🔊</span>
              <input
                type="range"
                min="0"
                max="100"
                value={volume() * 100}
                class="pulse-audio-player__volume-slider"
                onInput={handleVolumeChange}
              />
            </div>

            <div class="pulse-audio-player__speed">
              <span>Speed:</span>
              <div class="pulse-audio-player__speed-buttons">
                {[0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map(rate => (
                  <button
                    class={`pulse-audio-player__speed-btn ${playbackRate() === rate ? 'active' : ''}`}
                    onClick={() => handlePlaybackRateChange(rate)}
                  >
                    {rate}x
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      }>
        <div class="pulse-audio-player__error">
          <p>{error()}</p>
          <div class="pulse-audio-player__error-actions">
            <button onClick={() => {
              setError('');
              setShowExternalLink(false);
              if (audioRef) {
                audioRef.load();
              }
            }}>
              Try Again
            </button>
            <Show when={showExternalLink()}>
              <button
                onClick={() => {
                  if (props.item.enclosure?.url) {
                    window.open(props.item.enclosure.url, '_blank');
                  }
                }}
                class="pulse-audio-player__external-link"
              >
                Open Audio File
              </button>
            </Show>
          </div>
        </div>
      </Show>
    </div>
  );
};
