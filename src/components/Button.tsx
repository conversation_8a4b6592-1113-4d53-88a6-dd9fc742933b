import { JSX, splitProps } from 'solid-js';
import './Button.css';

export interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'tertiary' | 'destructive';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  loading?: boolean;
}

export function Button(props: ButtonProps) {
  const [local, others] = splitProps(props, [
    'variant',
    'size',
    'fullWidth',
    'loading',
    'children',
    'class',
    'disabled'
  ]);

  const classes = () => {
    const base = 'pulse-button';
    const variant = `pulse-button--${local.variant || 'primary'}`;
    const size = `pulse-button--${local.size || 'medium'}`;
    const fullWidth = local.fullWidth ? 'pulse-button--full-width' : '';
    const loading = local.loading ? 'pulse-button--loading' : '';
    const custom = local.class || '';
    
    return [base, variant, size, fullWidth, loading, custom]
      .filter(Boolean)
      .join(' ');
  };

  return (
    <button
      class={classes()}
      disabled={local.disabled || local.loading}
      {...others}
    >
      {local.loading && (
        <div class="pulse-button__spinner">
          <div class="pulse-spinner" />
        </div>
      )}
      <span class="pulse-button__content">
        {local.children}
      </span>
    </button>
  );
}
