.pulse-item-card {
  transition: all var(--pulse-transition-fast);
  border-left: 4px solid transparent;
}

.pulse-item-card--unread {
  border-left-color: var(--pulse-primary);
}

.pulse-item-card--read {
  opacity: 0.7;
}

.pulse-item-card__content {
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-sm);
}

.pulse-item-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--pulse-space-md);
}

.pulse-item-card__meta {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-tertiary);
  flex: 1;
  min-width: 0;
}

.pulse-item-card__type {
  flex-shrink: 0;
}

.pulse-item-card__feed {
  font-weight: 500;
  color: var(--pulse-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.pulse-item-card__date {
  white-space: nowrap;
  margin-left: auto;
}

.pulse-item-card__read-toggle {
  background: none;
  border: none;
  font-size: var(--pulse-text-lg);
  color: var(--pulse-text-quaternary);
  cursor: pointer;
  padding: var(--pulse-space-xs);
  border-radius: 50%;
  transition: all var(--pulse-transition-fast);
  flex-shrink: 0;
}

.pulse-item-card__read-toggle:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-secondary);
}

.pulse-item-card__read-toggle--read {
  color: var(--pulse-primary);
}

.pulse-item-card__title {
  margin: 0;
  font-size: var(--pulse-text-lg);
  font-weight: 600;
  color: var(--pulse-text-primary);
  line-height: var(--pulse-leading-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-item-card__description {
  margin: 0;
  font-size: var(--pulse-text-base);
  color: var(--pulse-text-secondary);
  line-height: var(--pulse-leading-normal);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pulse-item-card__media-info {
  display: flex;
  gap: var(--pulse-space-md);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-tertiary);
}

.pulse-item-card__duration,
.pulse-item-card__size {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-xs);
}

.pulse-item-card__categories {
  display: flex;
  gap: var(--pulse-space-xs);
  flex-wrap: wrap;
}

.pulse-item-card__category {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-secondary);
  font-size: var(--pulse-text-xs);
  padding: 2px 6px;
  border-radius: var(--pulse-radius-small);
  font-weight: 500;
}

.pulse-item-card__expanded {
  border-top: 1px solid var(--pulse-separator);
  padding-top: var(--pulse-space-md);
  margin-top: var(--pulse-space-sm);
  display: flex;
  flex-direction: column;
  gap: var(--pulse-space-md);
}

.pulse-item-card__full-content {
  font-size: var(--pulse-text-base);
  line-height: var(--pulse-leading-relaxed);
  color: var(--pulse-text-secondary);
}

.pulse-item-card__full-content h1,
.pulse-item-card__full-content h2,
.pulse-item-card__full-content h3,
.pulse-item-card__full-content h4,
.pulse-item-card__full-content h5,
.pulse-item-card__full-content h6 {
  color: var(--pulse-text-primary);
  margin: var(--pulse-space-md) 0 var(--pulse-space-sm) 0;
}

.pulse-item-card__full-content p {
  margin: var(--pulse-space-sm) 0;
}

.pulse-item-card__full-content a {
  color: var(--pulse-link);
}

.pulse-item-card__full-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--pulse-radius-medium);
  margin: var(--pulse-space-sm) 0;
}

.pulse-item-card__actions {
  display: flex;
  gap: var(--pulse-space-md);
  flex-wrap: wrap;
}

.pulse-item-card__link,
.pulse-item-card__play {
  background: none;
  border: 1px solid var(--pulse-separator-opaque);
  border-radius: var(--pulse-radius-medium);
  padding: var(--pulse-space-xs) var(--pulse-space-md);
  font-size: var(--pulse-text-sm);
  color: var(--pulse-text-secondary);
  cursor: pointer;
  transition: all var(--pulse-transition-fast);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--pulse-space-xs);
  font-family: var(--pulse-font-family);
}

.pulse-item-card__link:hover,
.pulse-item-card__play:hover {
  background-color: var(--pulse-background-tertiary);
  color: var(--pulse-text-primary);
  border-color: var(--pulse-primary);
}

.pulse-item-card__play {
  background-color: var(--pulse-primary);
  color: white;
  border-color: var(--pulse-primary);
}

.pulse-item-card__play:hover {
  background-color: var(--pulse-primary-dark);
  border-color: var(--pulse-primary-dark);
  color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .pulse-item-card__header {
    gap: var(--pulse-space-sm);
  }
  
  .pulse-item-card__meta {
    gap: var(--pulse-space-xs);
    font-size: var(--pulse-text-xs);
  }
  
  .pulse-item-card__feed {
    max-width: 100px;
  }
  
  .pulse-item-card__title {
    font-size: var(--pulse-text-base);
  }
  
  .pulse-item-card__description {
    font-size: var(--pulse-text-sm);
    -webkit-line-clamp: 2;
  }
  
  .pulse-item-card__actions {
    gap: var(--pulse-space-sm);
  }
  
  .pulse-item-card__link,
  .pulse-item-card__play {
    font-size: var(--pulse-text-xs);
    padding: var(--pulse-space-xs) var(--pulse-space-sm);
  }
}
