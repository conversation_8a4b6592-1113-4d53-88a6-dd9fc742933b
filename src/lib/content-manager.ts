import { RSSItem } from '../types';
import { db } from './database';

export class ContentManager {
  private static readonly MAX_DOWNLOADS = 3;
  private static readonly CLEANUP_THRESHOLD_DAYS = 7;

  // Download management
  static async downloadContent(item: RSSItem): Promise<boolean> {
    if (!item.enclosure?.url) {
      console.warn('No enclosure URL for item:', item.id);
      return false;
    }

    try {
      console.log('Downloading content for:', item.title);
      
      // Check if already downloaded
      const existingDownload = await db.getDownload(item.id);
      if (existingDownload) {
        console.log('Content already downloaded');
        return true;
      }

      // Fetch the content
      const response = await fetch(item.enclosure.url);
      if (!response.ok) {
        throw new Error(`Failed to download: ${response.status}`);
      }

      const blob = await response.blob();
      
      // Save to IndexedDB
      await db.saveDownload(item.id, blob);
      
      // Update item as downloaded
      const updatedItem = { ...item, isDownloaded: true };
      await db.updateItem(updatedItem);
      
      console.log('Content downloaded successfully');
      return true;
    } catch (error) {
      console.error('Download failed:', error);
      return false;
    }
  }

  static async getDownloadedContent(itemId: string): Promise<Blob | null> {
    try {
      const blob = await db.getDownload(itemId);
      return blob || null;
    } catch (error) {
      console.error('Failed to get downloaded content:', error);
      return null;
    }
  }

  static async removeDownload(itemId: string): Promise<void> {
    try {
      await db.deleteDownload(itemId);
      
      // Update item as not downloaded
      const item = await db.getItem(itemId);
      if (item) {
        const updatedItem = { ...item, isDownloaded: false };
        await db.updateItem(updatedItem);
      }
      
      console.log('Download removed for item:', itemId);
    } catch (error) {
      console.error('Failed to remove download:', error);
    }
  }

  // Auto-download management for latest episodes
  static async manageAutoDownloads(feedId: string): Promise<void> {
    try {
      const items = await db.getItemsByFeed(feedId, 10); // Get latest 10 items
      const undownloadedItems = items.filter(item => 
        !item.isDownloaded && 
        item.enclosure?.url &&
        !item.enclosure.type.includes('youtube') // Skip YouTube videos
      );

      // Download up to MAX_DOWNLOADS latest items
      const itemsToDownload = undownloadedItems
        .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime())
        .slice(0, ContentManager.MAX_DOWNLOADS);

      for (const item of itemsToDownload) {
        await this.downloadContent(item);
        
        // Add small delay between downloads to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Clean up old downloads beyond the limit
      await this.cleanupOldDownloads(feedId);
    } catch (error) {
      console.error('Auto-download management failed:', error);
    }
  }

  static async cleanupOldDownloads(feedId: string): Promise<void> {
    try {
      const items = await db.getItemsByFeed(feedId);
      const downloadedItems = items
        .filter(item => item.isDownloaded)
        .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());

      // Keep only the latest MAX_DOWNLOADS items
      const itemsToRemove = downloadedItems.slice(ContentManager.MAX_DOWNLOADS);
      
      for (const item of itemsToRemove) {
        await this.removeDownload(item.id);
      }

      console.log(`Cleaned up ${itemsToRemove.length} old downloads for feed ${feedId}`);
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }

  // Content streaming
  static createStreamingUrl(item: RSSItem): string | null {
    if (!item.enclosure?.url) return null;
    
    // For YouTube videos, return the original URL
    if (item.enclosure.type === 'video/youtube') {
      return item.enclosure.url;
    }
    
    // For other content, return the direct URL for streaming
    return item.enclosure.url;
  }

  static async getContentUrl(item: RSSItem): Promise<string | null> {
    if (!item.enclosure?.url) {
      console.warn('No enclosure URL for item:', item.id);
      return null;
    }

    // First, check if content is downloaded
    const downloadedBlob = await this.getDownloadedContent(item.id);
    if (downloadedBlob) {
      console.log('Using downloaded content for:', item.id);
      // Create object URL for downloaded content
      return URL.createObjectURL(downloadedBlob);
    }

    console.log('Using streaming URL for:', item.id);
    // Fall back to streaming
    return this.createStreamingUrl(item);
  }

  // Progress tracking
  static async updateProgress(itemId: string, currentTime: number, duration: number): Promise<void> {
    try {
      const item = await db.getItem(itemId);
      if (!item) return;

      // Mark as read if watched/listened to 80% or more
      const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
      
      if (progressPercentage >= 80 && !item.isRead) {
        await db.markItemAsRead(itemId);
        console.log('Item marked as read due to progress:', itemId);
      }
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  }

  // Cleanup consumed content
  static async cleanupConsumedContent(): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - ContentManager.CLEANUP_THRESHOLD_DAYS);

      // Get all downloaded items that are read and older than threshold
      const allItems = await db.getLatestItems(1000); // Get a large batch
      const itemsToCleanup = allItems.filter(item => 
        item.isDownloaded &&
        item.isRead &&
        item.pubDate < cutoffDate
      );

      let cleanedCount = 0;
      for (const item of itemsToCleanup) {
        await this.removeDownload(item.id);
        cleanedCount++;
      }

      console.log(`Cleaned up ${cleanedCount} consumed content items`);
      return cleanedCount;
    } catch (error) {
      console.error('Cleanup consumed content failed:', error);
      return 0;
    }
  }

  // Storage management
  static async getStorageInfo(): Promise<{
    totalSize: number;
    downloadCount: number;
    formattedSize: string;
  }> {
    try {
      const totalSize = await db.getDownloadSize();
      const allItems = await db.getLatestItems(1000);
      const downloadCount = allItems.filter(item => item.isDownloaded).length;
      
      const formattedSize = this.formatBytes(totalSize);
      
      return {
        totalSize,
        downloadCount,
        formattedSize
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        totalSize: 0,
        downloadCount: 0,
        formattedSize: '0 B'
      };
    }
  }

  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Background cleanup scheduler
  static startBackgroundCleanup(): void {
    // Run cleanup every 24 hours
    setInterval(async () => {
      console.log('Running background cleanup...');
      await this.cleanupConsumedContent();
    }, 24 * 60 * 60 * 1000);
  }

  // Manual download trigger
  static async downloadLatestEpisodes(feedId: string, count: number = 3): Promise<number> {
    try {
      const items = await db.getItemsByFeed(feedId, count * 2); // Get more to filter
      const downloadableItems = items
        .filter(item => 
          !item.isDownloaded && 
          item.enclosure?.url &&
          !item.enclosure.type.includes('youtube')
        )
        .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime())
        .slice(0, count);

      let downloadedCount = 0;
      for (const item of downloadableItems) {
        const success = await this.downloadContent(item);
        if (success) {
          downloadedCount++;
        }
        
        // Small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      return downloadedCount;
    } catch (error) {
      console.error('Manual download failed:', error);
      return 0;
    }
  }
}
