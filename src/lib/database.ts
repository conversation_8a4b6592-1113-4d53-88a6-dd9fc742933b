import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { DBFeed, DBItem, AppSettings, RSSFeed, RSSItem } from '../types';

interface PulseDB extends DBSchema {
  feeds: {
    key: string;
    value: DBFeed;
    indexes: { 'by-url': string; 'by-type': string; 'by-active': boolean };
  };
  items: {
    key: string;
    value: DBItem;
    indexes: { 
      'by-feed': string; 
      'by-date': string; 
      'by-read': boolean;
      'by-downloaded': boolean;
      'by-feed-date': [string, string];
    };
  };
  settings: {
    key: string;
    value: AppSettings;
  };
  downloads: {
    key: string;
    value: {
      id: string;
      itemId: string;
      blob: Blob;
      downloadDate: string;
      size: number;
    };
  };
}

class DatabaseManager {
  private db: IDBPDatabase<PulseDB> | null = null;
  private readonly DB_NAME = 'PulseDB';
  private readonly DB_VERSION = 1;

  async init(): Promise<void> {
    this.db = await openDB<PulseDB>(this.DB_NAME, this.DB_VERSION, {
      upgrade(db) {
        // Feeds store
        const feedStore = db.createObjectStore('feeds', { keyPath: 'id' });
        feedStore.createIndex('by-url', 'url', { unique: true });
        feedStore.createIndex('by-type', 'type');
        feedStore.createIndex('by-active', 'isActive');

        // Items store
        const itemStore = db.createObjectStore('items', { keyPath: 'id' });
        itemStore.createIndex('by-feed', 'feedId');
        itemStore.createIndex('by-date', 'pubDate');
        itemStore.createIndex('by-read', 'isRead');
        itemStore.createIndex('by-downloaded', 'isDownloaded');
        itemStore.createIndex('by-feed-date', ['feedId', 'pubDate']);

        // Settings store
        db.createObjectStore('settings', { keyPath: 'id' });

        // Downloads store for cached content
        db.createObjectStore('downloads', { keyPath: 'id' });
      },
    });
  }

  private ensureDB(): IDBPDatabase<PulseDB> {
    if (!this.db) {
      throw new Error('Database not initialized. Call init() first.');
    }
    return this.db;
  }

  // Feed operations
  async addFeed(feed: RSSFeed): Promise<void> {
    const db = this.ensureDB();
    const dbFeed: DBFeed = {
      ...feed,
      lastUpdated: feed.lastUpdated.toISOString(),
      subscriptionDate: feed.subscriptionDate.toISOString(),
    };
    await db.add('feeds', dbFeed);
  }

  async updateFeed(feed: RSSFeed): Promise<void> {
    const db = this.ensureDB();
    const dbFeed: DBFeed = {
      ...feed,
      lastUpdated: feed.lastUpdated.toISOString(),
      subscriptionDate: feed.subscriptionDate.toISOString(),
    };
    await db.put('feeds', dbFeed);
  }

  async getFeed(id: string): Promise<RSSFeed | undefined> {
    const db = this.ensureDB();
    const dbFeed = await db.get('feeds', id);
    if (!dbFeed) return undefined;
    
    return {
      ...dbFeed,
      lastUpdated: new Date(dbFeed.lastUpdated),
      subscriptionDate: new Date(dbFeed.subscriptionDate),
    };
  }

  async getAllFeeds(): Promise<RSSFeed[]> {
    const db = this.ensureDB();
    const dbFeeds = await db.getAll('feeds');
    return dbFeeds.map(dbFeed => ({
      ...dbFeed,
      lastUpdated: new Date(dbFeed.lastUpdated),
      subscriptionDate: new Date(dbFeed.subscriptionDate),
    }));
  }

  async getActiveFeeds(): Promise<RSSFeed[]> {
    const db = this.ensureDB();
    const dbFeeds = await db.getAllFromIndex('feeds', 'by-active', true);
    return dbFeeds.map(dbFeed => ({
      ...dbFeed,
      lastUpdated: new Date(dbFeed.lastUpdated),
      subscriptionDate: new Date(dbFeed.subscriptionDate),
    }));
  }

  async deleteFeed(id: string): Promise<void> {
    const db = this.ensureDB();
    const tx = db.transaction(['feeds', 'items', 'downloads'], 'readwrite');
    
    // Delete feed
    await tx.objectStore('feeds').delete(id);
    
    // Delete all items for this feed
    const items = await tx.objectStore('items').index('by-feed').getAll(id);
    for (const item of items) {
      await tx.objectStore('items').delete(item.id);
      // Delete associated downloads
      try {
        await tx.objectStore('downloads').delete(item.id);
      } catch (e) {
        // Download might not exist, ignore error
      }
    }
    
    await tx.done;
  }

  // Item operations
  async addItem(item: RSSItem): Promise<void> {
    const db = this.ensureDB();
    const dbItem: DBItem = {
      ...item,
      pubDate: item.pubDate.toISOString(),
    };
    await db.add('items', dbItem);
  }

  async addItems(items: RSSItem[]): Promise<void> {
    const db = this.ensureDB();
    const tx = db.transaction('items', 'readwrite');
    
    for (const item of items) {
      const dbItem: DBItem = {
        ...item,
        pubDate: item.pubDate.toISOString(),
      };
      await tx.store.add(dbItem);
    }
    
    await tx.done;
  }

  async updateItem(item: RSSItem): Promise<void> {
    const db = this.ensureDB();
    const dbItem: DBItem = {
      ...item,
      pubDate: item.pubDate.toISOString(),
    };
    await db.put('items', dbItem);
  }

  async getItem(id: string): Promise<RSSItem | undefined> {
    const db = this.ensureDB();
    const dbItem = await db.get('items', id);
    if (!dbItem) return undefined;
    
    return {
      ...dbItem,
      pubDate: new Date(dbItem.pubDate),
    };
  }

  async getItemsByFeed(feedId: string, limit?: number): Promise<RSSItem[]> {
    const db = this.ensureDB();
    const dbItems = await db.getAllFromIndex('items', 'by-feed', feedId);
    
    // Sort by date descending
    const sortedItems = dbItems
      .map(dbItem => ({
        ...dbItem,
        pubDate: new Date(dbItem.pubDate),
      }))
      .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime());
    
    return limit ? sortedItems.slice(0, limit) : sortedItems;
  }

  async getLatestItems(limit: number = 50): Promise<RSSItem[]> {
    const db = this.ensureDB();
    const dbItems = await db.getAll('items');
    
    return dbItems
      .map(dbItem => ({
        ...dbItem,
        pubDate: new Date(dbItem.pubDate),
      }))
      .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime())
      .slice(0, limit);
  }

  async markItemAsRead(id: string): Promise<void> {
    const item = await this.getItem(id);
    if (item) {
      item.isRead = true;
      await this.updateItem(item);
    }
  }

  async markItemAsUnread(id: string): Promise<void> {
    const item = await this.getItem(id);
    if (item) {
      item.isRead = false;
      await this.updateItem(item);
    }
  }

  // Settings operations
  async getSettings(): Promise<AppSettings> {
    const db = this.ensureDB();
    const settings = await db.get('settings', 'app');
    
    return settings || {
      theme: 'auto',
      updateFrequency: 6,
      maxDownloads: 3,
      autoCleanup: true,
      notifications: true,
      playbackRate: 1.0,
      volume: 0.8,
    };
  }

  async updateSettings(settings: AppSettings): Promise<void> {
    const db = this.ensureDB();
    await db.put('settings', { ...settings, id: 'app' });
  }

  // Download operations
  async saveDownload(itemId: string, blob: Blob): Promise<void> {
    const db = this.ensureDB();
    await db.put('downloads', {
      id: itemId,
      itemId,
      blob,
      downloadDate: new Date().toISOString(),
      size: blob.size,
    });
  }

  async getDownload(itemId: string): Promise<Blob | undefined> {
    const db = this.ensureDB();
    const download = await db.get('downloads', itemId);
    return download?.blob;
  }

  async deleteDownload(itemId: string): Promise<void> {
    const db = this.ensureDB();
    await db.delete('downloads', itemId);
  }

  async getDownloadSize(): Promise<number> {
    const db = this.ensureDB();
    const downloads = await db.getAll('downloads');
    return downloads.reduce((total, download) => total + download.size, 0);
  }

  // Cleanup operations
  async cleanupOldItems(keepDays: number = 30): Promise<number> {
    const db = this.ensureDB();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - keepDays);
    
    const allItems = await db.getAll('items');
    const itemsToDelete = allItems.filter(item => 
      new Date(item.pubDate) < cutoffDate && item.isRead
    );
    
    const tx = db.transaction(['items', 'downloads'], 'readwrite');
    
    for (const item of itemsToDelete) {
      await tx.objectStore('items').delete(item.id);
      try {
        await tx.objectStore('downloads').delete(item.id);
      } catch (e) {
        // Download might not exist, ignore error
      }
    }
    
    await tx.done;
    return itemsToDelete.length;
  }
}

export const db = new DatabaseManager();
