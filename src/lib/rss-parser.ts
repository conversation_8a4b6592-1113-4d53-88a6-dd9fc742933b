import { FeedValidationResult, ParsedFeed, ParsedItem, FeedType, MediaEnclosure } from '../types';

export class RSSParser {
  private static readonly CORS_PROXIES = [
    'https://api.allorigins.win/get?url=',
    'https://corsproxy.io/?',
    'https://api.codetabs.com/v1/proxy?quest=',
  ];

  async validateFeed(url: string): Promise<FeedValidationResult> {
    try {
      console.log('Fetching feed for validation:', url);
      const response = await this.fetchFeed(url);
      const text = await response.text();
      console.log('Feed content length:', text.length);
      console.log('Feed content preview:', text.substring(0, 500));

      if (!text.trim()) {
        return {
          isValid: false,
          error: 'Feed is empty or could not be retrieved'
        };
      }

      const parser = new DOMParser();
      const doc = parser.parseFromString(text, 'application/xml');

      // Check for parsing errors
      const parserError = doc.querySelector('parsererror');
      if (parserError) {
        console.error('XML parsing error:', parserError.textContent);
        return {
          isValid: false,
          error: 'Invalid XML format: ' + parserError.textContent
        };
      }

      // Detect feed type and validate structure
      const isRSS = doc.querySelector('rss') !== null;
      const feedElement = doc.querySelector('feed');
      const isAtom = feedElement !== null && (
        feedElement.getAttribute('xmlns') === 'http://www.w3.org/2005/Atom' ||
        feedElement.getAttribute('xmlns')?.includes('atom') ||
        doc.documentElement?.tagName.toLowerCase() === 'feed'
      );

      console.log('Feed detection - isRSS:', isRSS, 'isAtom:', isAtom);
      console.log('Root element:', doc.documentElement?.tagName);
      console.log('Feed element xmlns:', feedElement?.getAttribute('xmlns'));

      if (!isRSS && !isAtom) {
        // Check if it might be an Atom feed without proper namespace
        if (doc.documentElement?.tagName.toLowerCase() === 'feed') {
          console.log('Treating as Atom feed despite missing namespace');
          // Treat as Atom feed
        } else {
          return {
            isValid: false,
            error: 'Not a valid RSS or Atom feed - root element: ' + doc.documentElement?.tagName
          };
        }
      }

      const actuallyAtom = isAtom || doc.documentElement?.tagName.toLowerCase() === 'feed';
      const feedInfo = this.extractBasicFeedInfo(doc, isRSS);
      const feedType = this.detectFeedType(doc, isRSS);
      const itemCount = this.countItems(doc, isRSS);

      console.log('Feed validation successful:', { feedType, title: feedInfo.title, itemCount });

      return {
        isValid: true,
        feedType,
        title: feedInfo.title,
        description: feedInfo.description,
        itemCount
      };
    } catch (error) {
      console.error('Feed validation error:', error);
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async parseFeed(url: string): Promise<ParsedFeed> {
    const response = await this.fetchFeed(url);
    const text = await response.text();

    const parser = new DOMParser();
    const doc = parser.parseFromString(text, 'application/xml');

    const isRSS = doc.querySelector('rss') !== null;
    const isAtom = doc.documentElement?.tagName.toLowerCase() === 'feed' || doc.querySelector('feed') !== null;
    const feedType = this.detectFeedType(doc, isRSS);

    console.log('Parsing feed - isRSS:', isRSS, 'isAtom:', isAtom);

    if (isRSS) {
      return this.parseRSSFeed(doc, feedType);
    } else {
      return this.parseAtomFeed(doc, feedType);
    }
  }

  private async fetchFeed(url: string): Promise<Response> {
    // Try direct fetch first (will work for same-origin or CORS-enabled feeds)
    try {
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml',
        },
        mode: 'cors'
      });

      if (response.ok) {
        console.log('Direct fetch successful');
        return response;
      }
    } catch (error) {
      console.log('Direct fetch failed, trying CORS proxies...', error);
    }

    // Try multiple CORS proxies
    for (let i = 0; i < RSSParser.CORS_PROXIES.length; i++) {
      const proxy = RSSParser.CORS_PROXIES[i];
      try {
        const proxyUrl = proxy + encodeURIComponent(url);
        console.log(`Trying proxy ${i + 1}/${RSSParser.CORS_PROXIES.length}: ${proxy}`);

        const response = await fetch(proxyUrl, {
          headers: {
            'Accept': 'application/json, application/rss+xml, application/xml, text/xml, application/atom+xml, text/plain',
          },
          mode: 'cors'
        });

        if (response.ok) {
          console.log(`Success with proxy: ${proxy}`);

          // Handle allorigins.win response format
          if (proxy.includes('allorigins.win')) {
            const data = await response.json();
            if (data.contents) {
              // Create a new response with the actual content
              return new Response(data.contents, {
                status: 200,
                statusText: 'OK',
                headers: { 'Content-Type': 'application/xml' }
              });
            }
          }

          return response;
        }
      } catch (error) {
        console.log(`Proxy ${proxy} failed:`, error);
        continue;
      }
    }

    throw new Error('Failed to fetch feed: All proxies failed. The feed may be unavailable or have strict CORS policies.');
  }

  private extractBasicFeedInfo(doc: Document, isRSS: boolean): { title: string; description: string } {
    if (isRSS) {
      const channel = doc.querySelector('channel');
      return {
        title: this.getTextContent(channel?.querySelector('title')) || 'Untitled Feed',
        description: this.getTextContent(channel?.querySelector('description')) || ''
      };
    } else {
      return {
        title: this.getTextContent(doc.querySelector('feed > title')) || 'Untitled Feed',
        description: this.getTextContent(doc.querySelector('feed > subtitle')) || ''
      };
    }
  }

  private detectFeedType(doc: Document, isRSS: boolean): FeedType {
    // Check for YouTube feeds first
    const feedElement = isRSS ? doc.querySelector('channel') : (doc.querySelector('feed') || doc.documentElement);
    const feedLink = isRSS ?
      feedElement?.querySelector('link')?.textContent :
      feedElement?.querySelector('link[rel="alternate"]')?.getAttribute('href');

    if (feedLink && feedLink.includes('youtube.com')) {
      console.log('Detected YouTube feed');
      return 'video';
    }

    // Check for podcast-specific namespaces
    const hasItunesNamespace = doc.querySelector('[*|author], [*|duration], [*|category]') !== null;
    if (hasItunesNamespace) {
      console.log('Detected iTunes/podcast namespace');
      return 'audio';
    }

    const items = isRSS ?
      Array.from(doc.querySelectorAll('item')) :
      Array.from(doc.querySelectorAll('entry'));

    console.log(`Found ${items.length} items for type detection`);

    // Check first few items for media types
    const sampleItems = items.slice(0, 5);
    let audioCount = 0;
    let videoCount = 0;

    for (const item of sampleItems) {
      // Check for YouTube video links
      const itemLink = isRSS ?
        item.querySelector('link')?.textContent :
        item.querySelector('link[rel="alternate"]')?.getAttribute('href');

      if (itemLink && (itemLink.includes('youtube.com/watch') || itemLink.includes('youtu.be/'))) {
        console.log('Found YouTube video link:', itemLink);
        videoCount++;
        continue;
      }

      const enclosure = this.extractEnclosure(item, isRSS);
      if (enclosure) {
        console.log('Found enclosure:', enclosure.type);
        if (enclosure.type.startsWith('audio/')) {
          audioCount++;
        } else if (enclosure.type.startsWith('video/')) {
          videoCount++;
        }
      }

      // Check for media:content elements (common in video feeds)
      const mediaContent = item.querySelector('media\\:content, content');
      if (mediaContent) {
        const mediaType = mediaContent.getAttribute('type') || '';
        console.log('Found media content:', mediaType);
        if (mediaType.startsWith('video/')) {
          videoCount++;
        } else if (mediaType.startsWith('audio/')) {
          audioCount++;
        }
      }
    }

    console.log(`Type detection results - audio: ${audioCount}, video: ${videoCount}`);

    // Determine feed type based on majority content
    if (audioCount > videoCount && audioCount > 0) {
      return 'audio';
    } else if (videoCount > 0) {
      return 'video';
    } else {
      return 'text';
    }
  }

  private countItems(doc: Document, isRSS: boolean): number {
    return isRSS ? 
      doc.querySelectorAll('item').length :
      doc.querySelectorAll('entry').length;
  }

  private parseRSSFeed(doc: Document, feedType: FeedType): ParsedFeed {
    const channel = doc.querySelector('channel');
    
    const title = this.getTextContent(channel?.querySelector('title')) || 'Untitled Feed';
    const description = this.getTextContent(channel?.querySelector('description')) || '';
    const link = this.getTextContent(channel?.querySelector('link')) || '';
    const imageUrl = this.getTextContent(channel?.querySelector('image url')) || 
                     this.getTextContent(channel?.querySelector('itunes\\:image')) ||
                     channel?.querySelector('image')?.getAttribute('href') || undefined;

    const items = Array.from(doc.querySelectorAll('item')).map(item => 
      this.parseRSSItem(item)
    );

    return {
      title,
      description,
      link,
      imageUrl,
      items,
      type: feedType
    };
  }

  private parseAtomFeed(doc: Document, feedType: FeedType): ParsedFeed {
    const feed = doc.querySelector('feed');
    
    const title = this.getTextContent(feed?.querySelector('title')) || 'Untitled Feed';
    const description = this.getTextContent(feed?.querySelector('subtitle')) || '';
    const linkEl = feed?.querySelector('link[rel="alternate"]') || feed?.querySelector('link');
    const link = linkEl?.getAttribute('href') || '';
    const imageUrl = feed?.querySelector('logo')?.textContent || undefined;

    const items = Array.from(doc.querySelectorAll('entry')).map(entry => 
      this.parseAtomEntry(entry)
    );

    return {
      title,
      description,
      link,
      imageUrl,
      items,
      type: feedType
    };
  }

  private parseRSSItem(item: Element): ParsedItem {
    const title = this.getTextContent(item.querySelector('title')) || 'Untitled';
    const description = this.getTextContent(item.querySelector('description')) || '';
    const link = this.getTextContent(item.querySelector('link')) || '';
    const pubDateStr = this.getTextContent(item.querySelector('pubDate')) || '';
    const pubDate = pubDateStr ? new Date(pubDateStr) : new Date();
    const guid = this.getTextContent(item.querySelector('guid')) || link || title;
    const author = this.getTextContent(item.querySelector('author')) || 
                   this.getTextContent(item.querySelector('itunes\\:author')) || undefined;
    
    const categories = Array.from(item.querySelectorAll('category'))
      .map(cat => cat.textContent || '')
      .filter(Boolean);

    const enclosure = this.extractEnclosure(item, true);
    const content = this.getTextContent(item.querySelector('content\\:encoded')) || description;

    return {
      title,
      description,
      link,
      pubDate,
      guid,
      author,
      categories,
      enclosure,
      content
    };
  }

  private parseAtomEntry(entry: Element): ParsedItem {
    const title = this.getTextContent(entry.querySelector('title')) || 'Untitled';
    const summary = this.getTextContent(entry.querySelector('summary')) || '';
    const content = this.getTextContent(entry.querySelector('content')) || summary;
    const linkEl = entry.querySelector('link[rel="alternate"]') || entry.querySelector('link');
    const link = linkEl?.getAttribute('href') || '';
    const updatedStr = this.getTextContent(entry.querySelector('updated')) || 
                       this.getTextContent(entry.querySelector('published')) || '';
    const pubDate = updatedStr ? new Date(updatedStr) : new Date();
    const guid = this.getTextContent(entry.querySelector('id')) || link || title;
    const author = this.getTextContent(entry.querySelector('author name')) || undefined;
    
    const categories = Array.from(entry.querySelectorAll('category'))
      .map(cat => cat.getAttribute('term') || '')
      .filter(Boolean);

    const enclosure = this.extractEnclosure(entry, false);

    return {
      title,
      description: summary,
      link,
      pubDate,
      guid,
      author,
      categories,
      enclosure,
      content
    };
  }

  private extractEnclosure(item: Element, isRSS: boolean): MediaEnclosure | undefined {
    // Try standard enclosure first
    if (isRSS) {
      const enclosure = item.querySelector('enclosure');
      if (enclosure) {
        const url = enclosure.getAttribute('url');
        const type = enclosure.getAttribute('type');
        const length = enclosure.getAttribute('length');

        if (url && type) {
          return {
            url,
            type,
            length: length ? parseInt(length, 10) : undefined
          };
        }
      }
    } else {
      const link = item.querySelector('link[rel="enclosure"]');
      if (link) {
        const url = link.getAttribute('href');
        const type = link.getAttribute('type');
        const length = link.getAttribute('length');

        if (url && type) {
          return {
            url,
            type,
            length: length ? parseInt(length, 10) : undefined
          };
        }
      }
    }

    // Check for media:content elements (common in video feeds)
    const mediaContent = item.querySelector('media\\:content, content');
    if (mediaContent) {
      const url = mediaContent.getAttribute('url');
      const type = mediaContent.getAttribute('type');
      const fileSize = mediaContent.getAttribute('fileSize');

      if (url && type) {
        return {
          url,
          type,
          length: fileSize ? parseInt(fileSize, 10) : undefined
        };
      }
    }

    // For YouTube feeds, create a synthetic enclosure from the video link
    const itemLink = isRSS ?
      item.querySelector('link')?.textContent :
      item.querySelector('link[rel="alternate"]')?.getAttribute('href');

    if (itemLink && (itemLink.includes('youtube.com/watch') || itemLink.includes('youtu.be/'))) {
      return {
        url: itemLink,
        type: 'video/youtube',
        length: undefined
      };
    }

    return undefined;
  }

  private getTextContent(element: Element | null): string | undefined {
    return element?.textContent?.trim() || undefined;
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static sanitizeHtml(html: string): string {
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/javascript:/gi, '');
  }
}
