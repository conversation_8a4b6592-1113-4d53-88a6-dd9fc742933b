import { RSSParser } from './rss-parser';
import { ContentManager } from './content-manager';
import { db } from './database';
import { RSSItem } from '../types';

export class SyncManager {
  private static syncInterval: number | undefined;
  private static readonly SYNC_INTERVAL_HOURS = 6;
  private static readonly SYNC_INTERVAL_MS = SyncManager.SYNC_INTERVAL_HOURS * 60 * 60 * 1000;
  
  private static rssParser = new RSSParser();
  private static isSyncing = false;

  // Start the background sync scheduler
  static startBackgroundSync(): void {
    console.log('Starting background sync scheduler...');
    
    // Clear any existing interval
    if (SyncManager.syncInterval) {
      clearInterval(SyncManager.syncInterval);
    }

    // Calculate next sync time (next 6-hour mark in UTC)
    const now = new Date();
    const nextSyncTime = SyncManager.getNextSyncTime(now);
    const timeUntilNextSync = nextSyncTime.getTime() - now.getTime();

    console.log(`Next sync scheduled for: ${nextSyncTime.toISOString()}`);

    // Set timeout for the first sync, then start regular interval
    setTimeout(() => {
      SyncManager.performSync();
      
      // Start regular 6-hour interval
      SyncManager.syncInterval = setInterval(() => {
        SyncManager.performSync();
      }, SyncManager.SYNC_INTERVAL_MS);
    }, timeUntilNextSync);
  }

  // Calculate the next 6-hour mark in UTC (00:00, 06:00, 12:00, 18:00)
  private static getNextSyncTime(currentTime: Date): Date {
    const utcTime = new Date(currentTime.getTime());
    const currentHour = utcTime.getUTCHours();
    
    // Find the next 6-hour mark
    const nextSyncHour = Math.ceil((currentHour + 1) / 6) * 6;
    
    const nextSync = new Date(utcTime);
    nextSync.setUTCHours(nextSyncHour % 24, 0, 0, 0);
    
    // If we've wrapped to the next day
    if (nextSyncHour >= 24) {
      nextSync.setUTCDate(nextSync.getUTCDate() + 1);
    }
    
    return nextSync;
  }

  // Perform a full sync of all active feeds
  static async performSync(): Promise<{
    feedsUpdated: number;
    itemsAdded: number;
    errors: string[];
  }> {
    if (SyncManager.isSyncing) {
      console.log('Sync already in progress, skipping...');
      return { feedsUpdated: 0, itemsAdded: 0, errors: [] };
    }

    SyncManager.isSyncing = true;
    console.log('Starting background sync...');

    const result = {
      feedsUpdated: 0,
      itemsAdded: 0,
      errors: [] as string[]
    };

    try {
      const activeFeeds = await db.getActiveFeeds();
      console.log(`Syncing ${activeFeeds.length} active feeds...`);

      for (const feed of activeFeeds) {
        try {
          const syncResult = await SyncManager.syncFeed(feed.id);
          result.feedsUpdated++;
          result.itemsAdded += syncResult.newItemsCount;
          
          // Auto-download management for updated feeds
          if (syncResult.newItemsCount > 0) {
            await ContentManager.manageAutoDownloads(feed.id);
          }
        } catch (error) {
          const errorMessage = `Failed to sync feed "${feed.title}": ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(errorMessage);
          result.errors.push(errorMessage);
        }
      }

      console.log(`Sync completed: ${result.feedsUpdated} feeds updated, ${result.itemsAdded} new items`);
    } catch (error) {
      const errorMessage = `Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(errorMessage);
      result.errors.push(errorMessage);
    } finally {
      SyncManager.isSyncing = false;
    }

    return result;
  }

  // Sync a single feed
  static async syncFeed(feedId: string): Promise<{ newItemsCount: number }> {
    const feed = await db.getFeed(feedId);
    if (!feed || !feed.isActive) {
      throw new Error('Feed not found or inactive');
    }

    console.log(`Syncing feed: ${feed.title}`);

    // Parse the feed
    const parsedFeed = await SyncManager.rssParser.parseFeed(feed.url);
    
    // Get existing items for this feed
    const existingItems = await db.getItemsByFeed(feed.id);
    const existingGuids = new Set(existingItems.map(item => item.guid));
    
    // Find new items
    const newItems = parsedFeed.items
      .filter(item => !existingGuids.has(item.guid))
      .slice(0, 10) // Limit to 10 new items per sync
      .map(item => ({
        id: crypto.randomUUID(),
        feedId: feed.id,
        title: item.title,
        description: item.description,
        link: item.link,
        pubDate: item.pubDate,
        guid: item.guid,
        author: item.author,
        categories: item.categories,
        enclosure: item.enclosure,
        content: item.content,
        isRead: false,
        isDownloaded: false,
      } as RSSItem));

    if (newItems.length > 0) {
      await db.addItems(newItems);
      console.log(`Added ${newItems.length} new items for feed: ${feed.title}`);
    }

    // Update feed metadata
    const updatedFeed = {
      ...feed,
      lastUpdated: new Date(),
      title: parsedFeed.title, // Update title in case it changed
      description: parsedFeed.description,
      imageUrl: parsedFeed.imageUrl || feed.imageUrl,
    };
    await db.updateFeed(updatedFeed);

    return { newItemsCount: newItems.length };
  }

  // Manual sync trigger (for pull-to-refresh)
  static async manualSync(): Promise<{
    feedsUpdated: number;
    itemsAdded: number;
    errors: string[];
  }> {
    console.log('Manual sync triggered');
    return await SyncManager.performSync();
  }

  // Sync a specific feed manually
  static async manualSyncFeed(feedId: string): Promise<{ newItemsCount: number }> {
    console.log('Manual feed sync triggered for:', feedId);
    return await SyncManager.syncFeed(feedId);
  }

  // Check if sync is currently running
  static get isRunning(): boolean {
    return SyncManager.isSyncing;
  }

  // Stop background sync
  static stopBackgroundSync(): void {
    if (SyncManager.syncInterval) {
      clearInterval(SyncManager.syncInterval);
      SyncManager.syncInterval = undefined;
      console.log('Background sync stopped');
    }
  }

  // Get next scheduled sync time
  static getNextScheduledSync(): Date {
    return SyncManager.getNextSyncTime(new Date());
  }

  // Force sync now (ignores if already running)
  static async forceSyncNow(): Promise<{
    feedsUpdated: number;
    itemsAdded: number;
    errors: string[];
  }> {
    SyncManager.isSyncing = false; // Reset the flag
    return await SyncManager.performSync();
  }

  // Validate all feeds (useful for checking feed health)
  static async validateAllFeeds(): Promise<{
    validFeeds: number;
    invalidFeeds: number;
    errors: string[];
  }> {
    const result = {
      validFeeds: 0,
      invalidFeeds: 0,
      errors: [] as string[]
    };

    try {
      const allFeeds = await db.getAllFeeds();
      
      for (const feed of allFeeds) {
        try {
          const validation = await SyncManager.rssParser.validateFeed(feed.url);
          if (validation.isValid) {
            result.validFeeds++;
          } else {
            result.invalidFeeds++;
            result.errors.push(`Feed "${feed.title}" is invalid: ${validation.error}`);
          }
        } catch (error) {
          result.invalidFeeds++;
          result.errors.push(`Failed to validate feed "${feed.title}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      result.errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  // Get sync statistics
  static async getSyncStats(): Promise<{
    totalFeeds: number;
    activeFeeds: number;
    lastSyncTime: Date | null;
    nextSyncTime: Date;
    isRunning: boolean;
  }> {
    const allFeeds = await db.getAllFeeds();
    const activeFeeds = allFeeds.filter(feed => feed.isActive);
    
    // Get the most recent lastUpdated time from all feeds
    const lastSyncTime = activeFeeds.length > 0 
      ? new Date(Math.max(...activeFeeds.map(feed => feed.lastUpdated.getTime())))
      : null;

    return {
      totalFeeds: allFeeds.length,
      activeFeeds: activeFeeds.length,
      lastSyncTime,
      nextSyncTime: SyncManager.getNextScheduledSync(),
      isRunning: SyncManager.isSyncing
    };
  }
}
