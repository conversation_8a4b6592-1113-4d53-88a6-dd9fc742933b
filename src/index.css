/* iOS 18 Design System for Pulse */
:root {
  /* Primary Colors - Warm & Playful */
  --pulse-primary: #FF6B35;
  --pulse-primary-light: #FF8A5B;
  --pulse-primary-dark: #E55A2B;

  /* Secondary Colors */
  --pulse-secondary: #F7931E;
  --pulse-accent: #FFD23F;

  /* Neutral Colors */
  --pulse-background: #F2F2F7;
  --pulse-background-secondary: #FFFFFF;
  --pulse-background-tertiary: #F2F2F7;
  --pulse-background-grouped: #F2F2F7;

  /* Text Colors */
  --pulse-text-primary: #000000;
  --pulse-text-secondary: #3C3C43;
  --pulse-text-tertiary: #3C3C4399;
  --pulse-text-quaternary: #3C3C4366;

  /* System Colors */
  --pulse-separator: #3C3C4349;
  --pulse-separator-opaque: #C6C6C8;
  --pulse-link: #007AFF;
  --pulse-success: #34C759;
  --pulse-warning: #FF9500;
  --pulse-error: #FF3B30;

  /* Shadows */
  --pulse-shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --pulse-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --pulse-shadow-large: 0 8px 24px rgba(0, 0, 0, 0.2);

  /* Border Radius */
  --pulse-radius-small: 8px;
  --pulse-radius-medium: 12px;
  --pulse-radius-large: 16px;
  --pulse-radius-xl: 20px;

  /* Spacing */
  --pulse-space-xs: 4px;
  --pulse-space-sm: 8px;
  --pulse-space-md: 16px;
  --pulse-space-lg: 24px;
  --pulse-space-xl: 32px;
  --pulse-space-2xl: 48px;

  /* Typography */
  --pulse-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --pulse-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --pulse-text-xs: 12px;
  --pulse-text-sm: 14px;
  --pulse-text-base: 16px;
  --pulse-text-lg: 18px;
  --pulse-text-xl: 20px;
  --pulse-text-2xl: 24px;
  --pulse-text-3xl: 30px;
  --pulse-text-4xl: 36px;

  /* Line Heights */
  --pulse-leading-tight: 1.25;
  --pulse-leading-normal: 1.5;
  --pulse-leading-relaxed: 1.75;

  /* Transitions */
  --pulse-transition-fast: 150ms ease-out;
  --pulse-transition-normal: 250ms ease-out;
  --pulse-transition-slow: 350ms ease-out;
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --pulse-background: #000000;
    --pulse-background-secondary: #1C1C1E;
    --pulse-background-tertiary: #2C2C2E;
    --pulse-background-grouped: #000000;

    --pulse-text-primary: #FFFFFF;
    --pulse-text-secondary: #EBEBF5;
    --pulse-text-tertiary: #EBEBF599;
    --pulse-text-quaternary: #EBEBF566;

    --pulse-separator: #54545899;
    --pulse-separator-opaque: #38383A;
  }
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--pulse-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--pulse-background);
  color: var(--pulse-text-primary);
  font-size: var(--pulse-text-base);
  line-height: var(--pulse-leading-normal);
  min-height: 100vh;
  min-height: 100dvh;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: var(--pulse-leading-tight);
}

h1 { font-size: var(--pulse-text-4xl); }
h2 { font-size: var(--pulse-text-3xl); }
h3 { font-size: var(--pulse-text-2xl); }
h4 { font-size: var(--pulse-text-xl); }
h5 { font-size: var(--pulse-text-lg); }
h6 { font-size: var(--pulse-text-base); }

p {
  margin: 0;
  color: var(--pulse-text-secondary);
}

a {
  color: var(--pulse-link);
  text-decoration: none;
  transition: opacity var(--pulse-transition-fast);
}

a:hover {
  opacity: 0.7;
}

code {
  font-family: var(--pulse-font-family-mono);
}

/* Utility Classes */
.text-primary { color: var(--pulse-text-primary); }
.text-secondary { color: var(--pulse-text-secondary); }
.text-tertiary { color: var(--pulse-text-tertiary); }
.text-quaternary { color: var(--pulse-text-quaternary); }

.bg-primary { background-color: var(--pulse-background); }
.bg-secondary { background-color: var(--pulse-background-secondary); }
.bg-tertiary { background-color: var(--pulse-background-tertiary); }

.rounded-sm { border-radius: var(--pulse-radius-small); }
.rounded-md { border-radius: var(--pulse-radius-medium); }
.rounded-lg { border-radius: var(--pulse-radius-large); }
.rounded-xl { border-radius: var(--pulse-radius-xl); }

.shadow-sm { box-shadow: var(--pulse-shadow-small); }
.shadow-md { box-shadow: var(--pulse-shadow-medium); }
.shadow-lg { box-shadow: var(--pulse-shadow-large); }

/* Safe Area Support */
.safe-area-top { padding-top: env(safe-area-inset-top); }
.safe-area-bottom { padding-bottom: env(safe-area-inset-bottom); }
.safe-area-left { padding-left: env(safe-area-inset-left); }
.safe-area-right { padding-right: env(safe-area-inset-right); }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--pulse-separator);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--pulse-separator-opaque);
}
