.pulse-app {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
  background-color: var(--pulse-background);
}

.pulse-app__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  min-height: 100dvh;
  gap: var(--pulse-space-lg);
  color: var(--pulse-text-secondary);
}

.pulse-app__loading .pulse-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--pulse-separator);
  border-top: 3px solid var(--pulse-primary);
  border-radius: 50%;
  animation: pulse-spin 1s linear infinite;
}

.pulse-app__header {
  background-color: var(--pulse-background-secondary);
  border-bottom: 1px solid var(--pulse-separator);
  position: sticky;
  top: 0;
  z-index: 100;
}

.pulse-app__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--pulse-space-md) var(--pulse-space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.pulse-app__title {
  display: flex;
  align-items: center;
  gap: var(--pulse-space-sm);
  font-size: var(--pulse-text-2xl);
  font-weight: 700;
  color: var(--pulse-text-primary);
  margin: 0;
}

.pulse-app__logo {
  font-size: var(--pulse-text-3xl);
}

.pulse-app__main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pulse-app__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--pulse-space-2xl);
  min-height: 60vh;
  gap: var(--pulse-space-lg);
}

.pulse-app__empty-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.pulse-app__empty h2 {
  color: var(--pulse-text-primary);
  margin: 0;
}

.pulse-app__empty p {
  color: var(--pulse-text-secondary);
  max-width: 400px;
  line-height: var(--pulse-leading-relaxed);
}

.pulse-app__content {
  display: grid;
  grid-template-columns: 300px 1fr;
  min-height: 0;
  flex: 1;
}

.pulse-app__sidebar {
  background-color: var(--pulse-background-secondary);
  border-right: 1px solid var(--pulse-separator);
  overflow-y: auto;
}

.pulse-app__feed-content {
  overflow-y: auto;
  background-color: var(--pulse-background);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pulse-app__header-content {
    padding: var(--pulse-space-sm) var(--pulse-space-md);
  }
  
  .pulse-app__title {
    font-size: var(--pulse-text-xl);
  }
  
  .pulse-app__content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .pulse-app__sidebar {
    border-right: none;
    border-bottom: 1px solid var(--pulse-separator);
    max-height: 200px;
  }
  
  .pulse-app__empty {
    padding: var(--pulse-space-xl);
  }
  
  .pulse-app__empty-icon {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .pulse-app__header-content {
    flex-direction: column;
    gap: var(--pulse-space-md);
    align-items: stretch;
  }
  
  .pulse-app__title {
    justify-content: center;
  }
}
