import { Component, onMount, Show, createSignal } from 'solid-js';
import { appStore } from './stores/app-store';
import { AddFeedModal } from './components/AddFeedModal';
import { FeedList } from './components/FeedList';
import { ItemList } from './components/ItemList';
import { Button } from './components/Button';
import './App.css';

const App: Component = () => {
  const [showAddFeed, setShowAddFeed] = createSignal(false);

  onMount(async () => {
    await appStore.init();
  });

  return (
    <div class="pulse-app">
      <Show when={!appStore.isInitializing} fallback={
        <div class="pulse-app__loading">
          <div class="pulse-spinner" />
          <p>Initializing Pulse...</p>
        </div>
      }>
        <header class="pulse-app__header safe-area-top">
          <div class="pulse-app__header-content">
            <h1 class="pulse-app__title">
              <span class="pulse-app__logo">📡</span>
              Pulse
            </h1>
            <Button
              variant="primary"
              size="medium"
              onClick={() => setShowAddFeed(true)}
            >
              Add Feed
            </Button>
          </div>
        </header>

        <main class="pulse-app__main">
          <Show when={appStore.state.feeds.length > 0} fallback={
            <div class="pulse-app__empty">
              <div class="pulse-app__empty-icon">📡</div>
              <h2>Welcome to Pulse!</h2>
              <p>Start by adding your first RSS feed to stay updated with your favorite content.</p>
              <Button
                variant="primary"
                size="large"
                onClick={() => setShowAddFeed(true)}
              >
                Add Your First Feed
              </Button>
            </div>
          }>
            <div class="pulse-app__content">
              <aside class="pulse-app__sidebar">
                <FeedList />
              </aside>
              <section class="pulse-app__feed-content">
                <ItemList />
              </section>
            </div>
          </Show>
        </main>

        <Show when={showAddFeed()}>
          <AddFeedModal onClose={() => setShowAddFeed(false)} />
        </Show>
      </Show>
    </div>
  );
};

export default App;
