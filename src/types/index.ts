// Core RSS Feed Types
export interface RSSFeed {
  id: string;
  url: string;
  title: string;
  description: string;
  link: string;
  lastUpdated: Date;
  imageUrl?: string;
  type: FeedType;
  subscriptionDate: Date;
  updateFrequency: number; // in hours
  isActive: boolean;
}

export interface RSSItem {
  id: string;
  feedId: string;
  title: string;
  description: string;
  link: string;
  pubDate: Date;
  guid: string;
  author?: string;
  categories: string[];
  enclosure?: MediaEnclosure;
  content?: string;
  isRead: boolean;
  isDownloaded: boolean;
  downloadPath?: string;
  duration?: number; // for audio/video in seconds
  fileSize?: number; // in bytes
}

export interface MediaEnclosure {
  url: string;
  type: string;
  length?: number;
}

export type FeedType = 'text' | 'audio' | 'video';

export type ContentType = 'article' | 'podcast' | 'video';

// UI State Types
export interface AppState {
  feeds: RSSFeed[];
  items: RSSItem[];
  selectedFeed?: string;
  selectedItem?: string;
  isLoading: boolean;
  error?: string;
  lastSync: Date;
}

export interface PlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  currentItem?: RSSItem;
}

// Database Schema Types
export interface DBFeed extends Omit<RSSFeed, 'lastUpdated' | 'subscriptionDate'> {
  lastUpdated: string;
  subscriptionDate: string;
}

export interface DBItem extends Omit<RSSItem, 'pubDate'> {
  pubDate: string;
}

// API Response Types
export interface FeedValidationResult {
  isValid: boolean;
  feedType?: FeedType;
  title?: string;
  description?: string;
  error?: string;
  itemCount?: number;
}

export interface ParsedFeed {
  title: string;
  description: string;
  link: string;
  imageUrl?: string;
  items: ParsedItem[];
  type: FeedType;
}

export interface ParsedItem {
  title: string;
  description: string;
  link: string;
  pubDate: Date;
  guid: string;
  author?: string;
  categories: string[];
  enclosure?: MediaEnclosure;
  content?: string;
}

// Settings Types
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  updateFrequency: number; // in hours
  maxDownloads: number;
  autoCleanup: boolean;
  notifications: boolean;
  playbackRate: number;
  volume: number;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Sync Types
export interface SyncStatus {
  isRunning: boolean;
  lastSync: Date;
  nextSync: Date;
  feedsUpdated: number;
  itemsAdded: number;
  errors: AppError[];
}
