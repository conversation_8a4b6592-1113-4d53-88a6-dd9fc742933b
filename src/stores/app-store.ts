import { createStore } from 'solid-js/store';
import { createSignal } from 'solid-js';
import { RSSFeed, RSSItem, AppSettings, PlayerState, AppError } from '../types';
import { db } from '../lib/database';
import { RSSParser } from '../lib/rss-parser';
import { ContentManager } from '../lib/content-manager';
import { SyncManager } from '../lib/sync-manager';

// App State Store
const [appState, setAppState] = createStore({
  feeds: [] as RSSFeed[],
  items: [] as RSSItem[],
  selectedFeed: undefined as string | undefined,
  selectedItem: undefined as string | undefined,
  isLoading: false,
  error: undefined as string | undefined,
  lastSync: new Date(),
});

// Player State Store
const [playerState, setPlayerState] = createStore<PlayerState>({
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 0.8,
  playbackRate: 1.0,
  currentItem: undefined,
});

// Settings Store
const [settings, setSettings] = createStore<AppSettings>({
  theme: 'auto',
  updateFrequency: 6,
  maxDownloads: 3,
  autoCleanup: true,
  notifications: true,
  playbackRate: 1.0,
  volume: 0.8,
});

// Loading states
const [isInitializing, setIsInitializing] = createSignal(false);
const [isSyncing, setIsSyncing] = createSignal(false);

// RSS Parser instance
const rssParser = new RSSParser();

// App Store Actions
export const appStore = {
  // State getters
  get state() { return appState; },
  get player() { return playerState; },
  get settings() { return settings; },
  get isInitializing() { return isInitializing(); },
  get isSyncing() { return isSyncing(); },

  // Initialization
  async init() {
    setIsInitializing(true);
    try {
      await db.init();
      
      // Load settings
      const savedSettings = await db.getSettings();
      setSettings(savedSettings);
      
      // Load feeds and items
      const feeds = await db.getAllFeeds();
      const items = await db.getLatestItems(100);
      
      setAppState({
        feeds,
        items,
        lastSync: new Date(),
      });
      
      // Start background sync and cleanup
      SyncManager.startBackgroundSync();
      ContentManager.startBackgroundCleanup();
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setAppState('error', 'Failed to initialize application');
    } finally {
      setIsInitializing(false);
    }
  },

  // Feed Management
  async validateFeedUrl(url: string) {
    if (!RSSParser.isValidUrl(url)) {
      return {
        isValid: false,
        error: 'Please enter a valid URL'
      };
    }

    setAppState('isLoading', true);
    try {
      const result = await rssParser.validateFeed(url);
      return result;
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Failed to validate feed'
      };
    } finally {
      setAppState('isLoading', false);
    }
  },

  async subscribeTo(url: string) {
    setAppState('isLoading', true);
    setAppState('error', undefined);
    
    try {
      // Check if already subscribed
      const existingFeed = appState.feeds.find(feed => feed.url === url);
      if (existingFeed) {
        throw new Error('You are already subscribed to this feed');
      }

      // Parse the feed
      const parsedFeed = await rssParser.parseFeed(url);
      
      // Create feed record
      const feed: RSSFeed = {
        id: crypto.randomUUID(),
        url,
        title: parsedFeed.title,
        description: parsedFeed.description,
        link: parsedFeed.link,
        lastUpdated: new Date(),
        imageUrl: parsedFeed.imageUrl,
        type: parsedFeed.type,
        subscriptionDate: new Date(),
        updateFrequency: settings.updateFrequency,
        isActive: true,
      };

      // Save feed to database
      await db.addFeed(feed);
      
      // Process items (limit to latest 3 initially)
      const latestItems = parsedFeed.items
        .sort((a, b) => b.pubDate.getTime() - a.pubDate.getTime())
        .slice(0, 3);
      
      const items: RSSItem[] = latestItems.map(item => ({
        id: crypto.randomUUID(),
        feedId: feed.id,
        title: item.title,
        description: item.description,
        link: item.link,
        pubDate: item.pubDate,
        guid: item.guid,
        author: item.author,
        categories: item.categories,
        enclosure: item.enclosure,
        content: item.content,
        isRead: false,
        isDownloaded: false,
      }));

      // Save items to database
      if (items.length > 0) {
        await db.addItems(items);
      }

      // Update state
      setAppState('feeds', feeds => [...feeds, feed]);
      setAppState('items', currentItems => [...items, ...currentItems]);

      // Start auto-download management for this feed
      ContentManager.manageAutoDownloads(feed.id);

      return { success: true, feed, itemCount: items.length };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe to feed';
      setAppState('error', errorMessage);
      throw error;
    } finally {
      setAppState('isLoading', false);
    }
  },

  async unsubscribeFrom(feedId: string) {
    try {
      await db.deleteFeed(feedId);
      
      // Update state
      setAppState('feeds', feeds => feeds.filter(f => f.id !== feedId));
      setAppState('items', items => items.filter(i => i.feedId !== feedId));
      
      // Clear selection if it was the deleted feed
      if (appState.selectedFeed === feedId) {
        setAppState('selectedFeed', undefined);
      }
    } catch (error) {
      console.error('Failed to unsubscribe:', error);
      setAppState('error', 'Failed to unsubscribe from feed');
    }
  },

  // Item Management
  async markAsRead(itemId: string) {
    try {
      await db.markItemAsRead(itemId);
      setAppState('items', items => 
        items.map(item => 
          item.id === itemId ? { ...item, isRead: true } : item
        )
      );
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  },

  async markAsUnread(itemId: string) {
    try {
      await db.markItemAsUnread(itemId);
      setAppState('items', items => 
        items.map(item => 
          item.id === itemId ? { ...item, isRead: false } : item
        )
      );
    } catch (error) {
      console.error('Failed to mark as unread:', error);
    }
  },

  // Sync Management
  async syncFeeds() {
    if (isSyncing()) return;

    setIsSyncing(true);
    try {
      const result = await SyncManager.manualSync();

      // Refresh feeds and items from database
      const feeds = await db.getAllFeeds();
      const items = await db.getLatestItems(100);

      setAppState({
        feeds,
        items,
        lastSync: new Date(),
      });

      return result;
    } finally {
      setIsSyncing(false);
    }
  },

  async syncSingleFeed(feedId: string) {
    try {
      const result = await SyncManager.manualSyncFeed(feedId);

      // Refresh items from database
      const items = await db.getLatestItems(100);
      setAppState('items', items);

      // Update the specific feed's lastUpdated time
      const updatedFeed = await db.getFeed(feedId);
      if (updatedFeed) {
        setAppState('feeds', feeds =>
          feeds.map(f => f.id === feedId ? updatedFeed : f)
        );
      }

      return result;
    } catch (error) {
      console.error('Failed to sync feed:', error);
      throw error;
    }
  },

  async getSyncStats() {
    return await SyncManager.getSyncStats();
  },

  // UI State Management
  selectFeed(feedId: string | undefined) {
    setAppState('selectedFeed', feedId);
  },

  selectItem(itemId: string | undefined) {
    setAppState('selectedItem', itemId);
  },

  clearError() {
    setAppState('error', undefined);
  },

  // Content Management
  async downloadContent(itemId: string) {
    const item = appState.items.find(i => i.id === itemId);
    if (!item) return false;

    const success = await ContentManager.downloadContent(item);
    if (success) {
      setAppState('items', items =>
        items.map(i => i.id === itemId ? { ...i, isDownloaded: true } : i)
      );
    }
    return success;
  },

  async removeDownload(itemId: string) {
    await ContentManager.removeDownload(itemId);
    setAppState('items', items =>
      items.map(i => i.id === itemId ? { ...i, isDownloaded: false } : i)
    );
  },

  async getContentUrl(item: RSSItem): Promise<string | null> {
    return await ContentManager.getContentUrl(item);
  },

  async updateProgress(itemId: string, currentTime: number, duration: number) {
    await ContentManager.updateProgress(itemId, currentTime, duration);

    // Update local state if item was marked as read
    const item = await db.getItem(itemId);
    if (item?.isRead) {
      setAppState('items', items =>
        items.map(i => i.id === itemId ? { ...i, isRead: true } : i)
      );
    }
  },

  async getStorageInfo() {
    return await ContentManager.getStorageInfo();
  },

  async cleanupConsumedContent() {
    const cleanedCount = await ContentManager.cleanupConsumedContent();

    // Refresh items to reflect cleanup
    const items = await db.getLatestItems(100);
    setAppState('items', items);

    return cleanedCount;
  },

  async downloadLatestEpisodes(feedId: string, count: number = 3) {
    const downloadedCount = await ContentManager.downloadLatestEpisodes(feedId, count);

    // Refresh items to reflect downloads
    const items = await db.getLatestItems(100);
    setAppState('items', items);

    return downloadedCount;
  },

  // Settings Management
  async updateSettings(newSettings: Partial<AppSettings>) {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    await db.updateSettings(updatedSettings);
  },
};
